# 🚀 Avvio Rapido - Smorfia Dreams

## ⚡ Setup in 5 minuti

### 1. Avvia Server Locale
```bash
# Con Python
python -m http.server 8000

# Con Node.js
npx serve .

# Con PHP
php -S localhost:8000
```

### 2. Apri nel <PERSON>rowser
```
http://localhost:8000
```

### 3. Test Base
- ✅ Prova input testuale con: "Ho sognato di volare sopra il mare"
- ✅ Controlla che i numeri vengano generati
- ✅ Verifica che la PWA sia installabile

## 🔑 Per API Google Gemini

### Opzione A: Demo (Senza AI)
L'app funziona anche senza API, generando numeri casuali dalla smorfia.

### Opzione B: API Completa
1. Ottieni API key: https://makersuite.google.com/app/apikey
2. Configura proxy server (vedi README.md)
3. Modifica endpoint in `js/ai.js`

## 📱 Installazione PWA
1. Apri l'app nel browser
2. Cerca il pulsante "Installa" 
3. Conferma installazione
4. Usa come app nativa!

## 🎯 Funzionalità Principali
- 📝 Inserimento sogno (testo o voce)
- 🤖 Interpretazione AI 
- 🎲 Numeri smorfia napoletana
- 💾 Salvataggio locale storico
- 📤 Condivisione risultati
- 🔄 Funziona offline

## 🚨 Risoluzione Problemi

**L'app non si carica?**
- Verifica server locale attivo
- Controlla console browser (F12)

**Speech recognition non funziona?**
- Usa HTTPS (necessario per microfono)
- Concedi permessi microfono

**API non risponde?**
- Configura API key Google Gemini
- Oppure usa in modalità demo

## 📞 Supporto
- 📖 Leggi README.md completo
- 🧪 Usa test-checklist.md per verifiche
- ⚙️ Configura con js/config.js

---

**Fatto! 🎉 La tua PWA Smorfia Dreams è pronta!**

Buona interpretazione dei sogni! 🌙✨