<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Interpreta il tuo sogno con l'intelligenza artificiale e scopri i numeri della smorfia per il lotto">
    <meta name="theme-color" content="#1e40af">

    <title>Smorf-IA - Interpreta il tuo sogno con l'intelligenza artificiale</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- CSS -->
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- Mobile PWA -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Smorf-IA">
    <link rel="apple-touch-icon" href="assets/icon-144.svg">

    <!-- Preload critici -->
    <link rel="preconnect" href="https://generativelanguage.googleapis.com">
</head>
<body>
    <!-- Header -->
    <header class="app-header">
        <div class="container">
            <h1 class="app-title">
                🤖 Smorf-IA
            </h1>
            <p class="app-subtitle">Interpreta il tuo sogno con l'intelligenza artificiale</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Welcome Screen -->
            <section id="welcome-screen" class="screen active">
                <div class="welcome-card">
                    <div class="welcome-icon">🎯</div>
                    <h2>Scopri i numeri del lotto dai tuoi sogni</h2>
                    <p>Racconta il tuo sogno e l'intelligenza artificiale lo interpreterà secondo la tradizione della smorfia napoletana.</p>

                    <div class="input-choice">
                        <button id="text-input-btn" class="choice-btn">
                            <span class="btn-icon">✍️</span>
                            <span class="btn-text">Scrivi il sogno</span>
                        </button>

                        <button id="voice-input-btn" class="choice-btn">
                            <span class="btn-icon">🎤</span>
                            <span class="btn-text">Racconta a voce</span>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Text Input Screen -->
            <section id="text-input-screen" class="screen">
                <div class="input-card">
                    <button class="back-btn" id="back-from-text">← Indietro</button>

                    <h2>Scrivi il tuo sogno</h2>
                    <p>Descrivi il più dettagliatamente possibile cosa hai sognato...</p>

                    <div class="text-input-container">
                        <textarea
                            id="dream-text"
                            placeholder="Es: Ho sognato di volare sopra il mare blu, c'erano dei pesci dorati che saltavano... Racconta tutti i dettagli che ricordi."
                            maxlength="1000"
                            rows="8">
                        </textarea>
                        <div class="char-count">
                            <span id="char-counter">0</span>/1000
                        </div>
                    </div>

                    <button id="analyze-text-btn" class="primary-btn" disabled>
                        Interpreta il sogno
                    </button>
                </div>
            </section>

            <!-- Voice Input Screen -->
            <section id="voice-input-screen" class="screen">
                <div class="input-card">
                    <button class="back-btn" id="back-from-voice">← Indietro</button>

                    <h2>Racconta il tuo sogno</h2>
                    <p>Premi il bottone e racconta il tuo sogno a voce</p>

                    <div class="voice-input-container">
                        <div class="voice-visualizer" id="voice-visualizer">
                            <div class="pulse-circle"></div>
                            <div class="voice-icon">🎤</div>
                        </div>

                        <button id="record-btn" class="record-btn">
                            <span class="btn-text">Inizia registrazione</span>
                        </button>

                        <div id="recording-status" class="recording-status hidden">
                            <div class="recording-indicator">
                                <span class="recording-dot"></span>
                                In registrazione... <span id="recording-timer">00:00</span>
                            </div>
                        </div>

                        <div id="transcription-area" class="transcription-area hidden">
                            <h3>Trascrizione:</h3>
                            <div id="transcription-text" class="transcription-text"></div>
                            <button id="analyze-voice-btn" class="primary-btn">
                                Interpreta il sogno
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Loading Screen -->
            <section id="loading-screen" class="screen">
                <div class="loading-card">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                    <h2>L'AI sta interpretando il tuo sogno...</h2>
                    <p id="loading-message">Analizzando i simboli onirici secondo la tradizione della smorfia</p>

                    <div class="loading-steps">
                        <div class="step active" id="step-1">
                            <span class="step-icon">🔍</span>
                            <span class="step-text">Analisi del sogno</span>
                        </div>
                        <div class="step" id="step-2">
                            <span class="step-icon">📚</span>
                            <span class="step-text">Consultazione smorfia</span>
                        </div>
                        <div class="step" id="step-3">
                            <span class="step-icon">🎯</span>
                            <span class="step-text">Generazione numeri</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Screen -->
            <section id="results-screen" class="screen">
                <div class="results-card">
                    <div class="results-header">
                        <h2>🎯 I tuoi numeri fortunati</h2>
                        <p>Basati sull'interpretazione AI del tuo sogno</p>
                    </div>

                    <div class="numbers-container" id="numbers-container">
                        <!-- Numeri generati dinamicamente -->
                    </div>

                    <div class="interpretation-container">
                        <h3>💭 Interpretazione del sogno</h3>
                        <div id="dream-interpretation" class="interpretation-text">
                            <!-- Interpretazione generata dall'AI -->
                        </div>
                    </div>

                    <div class="symbols-container">
                        <h3>🔍 Simboli identificati</h3>
                        <div id="symbols-list" class="symbols-list">
                            <!-- Lista simboli -->
                        </div>
                    </div>

                    <div class="actions-container">
                        <button id="save-result-btn" class="secondary-btn">
                            💾 Salva risultato
                        </button>
                        <button id="share-result-btn" class="secondary-btn">
                            📤 Condividi
                        </button>
                        <button id="new-dream-btn" class="primary-btn">
                            🌙 Nuovo sogno
                        </button>
                    </div>
                </div>
            </section>

            <!-- History Screen -->
            <section id="history-screen" class="screen">
                <div class="history-card">
                    <button class="back-btn" id="back-from-history">← Indietro</button>

                    <h2>📚 Storico dei sogni</h2>
                    <p>I tuoi sogni interpretati e i numeri generati</p>

                    <div id="history-list" class="history-list">
                        <!-- Lista storico sogni -->
                    </div>

                    <div class="history-empty hidden" id="history-empty">
                        <div class="empty-icon">📖</div>
                        <p>Nessun sogno salvato ancora</p>
                        <button class="primary-btn" onclick="showScreen('welcome-screen')">
                            Interpreta il primo sogno
                        </button>
                    </div>
                </div>
            </section>

        </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="nav-container">
            <button class="nav-btn active" id="nav-home">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">Home</span>
            </button>
            <button class="nav-btn" id="nav-history">
                <span class="nav-icon">📚</span>
                <span class="nav-text">Storico</span>
            </button>
            <button class="nav-btn" id="nav-info">
                <span class="nav-icon">ℹ️</span>
                <span class="nav-text">Info</span>
            </button>
        </div>
    </nav>

    <!-- Toast Messages -->
    <div id="toast-container" class="toast-container"></div>

    <!-- JavaScript Modules -->
    <script type="module" src="js/app.js"></script>

    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then((registration) => {
                        console.log('SW registrato con successo:', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registrazione fallita:', registrationError);
                    });
            });
        }
    </script>
</body>
</html>