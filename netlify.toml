[build]
  # Directory di pubblicazione (root del progetto)
  publish = "."

  # Comando di build (non necessario per questo progetto statico)
  command = "echo 'Build completato'"

[functions]
  # Directory delle funzioni Netlify
  directory = "netlify/functions"

  # Runtime Node.js
  node_bundler = "esbuild"

# Configurazione per PWA - redirect per SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers di sicurezza
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(self), geolocation=()"

# Headers specifici per il Service Worker
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
    Service-Worker-Allowed = "/"

# Headers per il manifest PWA
[[headers]]
  for = "/manifest.json"
  [headers.values]
    Content-Type = "application/manifest+json"
    Cache-Control = "public, max-age=86400"

# Headers per le risorse statiche
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/css/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Configurazione variabili d'ambiente (esempio)
[context.production.environment]
  NODE_VERSION = "18"

[context.deploy-preview.environment]
  NODE_VERSION = "18"

[context.branch-deploy.environment]
  NODE_VERSION = "18"
