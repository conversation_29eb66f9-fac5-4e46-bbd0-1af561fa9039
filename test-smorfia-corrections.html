<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Correzioni Smorfia</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .number-display {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 2px;
            font-weight: bold;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Correzioni Sistema Smorfia</h1>
    
    <div class="test-section">
        <h2>📊 Stato del Sistema</h2>
        <div id="system-status"></div>
    </div>

    <div class="test-section">
        <h2>🎯 Test Database Smorfia</h2>
        <div id="database-test"></div>
    </div>

    <div class="test-section">
        <h2>🤖 Test Correzione Interpretazioni</h2>
        <div id="interpretation-test"></div>
    </div>

    <div class="test-section">
        <h2>📝 Test Numeri Specifici</h2>
        <div id="numbers-test"></div>
    </div>

    <script type="module">
        // Simula il caricamento dei moduli
        import { SmorfiaService } from './js/smorfia.js';
        import { AIService } from './js/ai.js';

        class TestSuite {
            constructor() {
                this.smorfiaService = new SmorfiaService();
                this.aiService = new AIService();
                this.results = [];
            }

            async init() {
                try {
                    await this.smorfiaService.init();
                    this.aiService.setSmorfiaDatabase(this.smorfiaService.smorfiaData);
                    this.showSystemStatus(true);
                    this.runAllTests();
                } catch (error) {
                    this.showSystemStatus(false, error.message);
                }
            }

            showSystemStatus(success, error = null) {
                const statusEl = document.getElementById('system-status');
                if (success) {
                    statusEl.innerHTML = `
                        <div class="test-result success">
                            ✅ Sistema inizializzato correttamente
                            <br>📚 Database smorfia: ${Object.keys(this.smorfiaService.smorfiaData).length} numeri caricati
                            <br>🤖 AI Service: Database collegato
                        </div>
                    `;
                } else {
                    statusEl.innerHTML = `
                        <div class="test-result error">
                            ❌ Errore inizializzazione: ${error}
                        </div>
                    `;
                }
            }

            runAllTests() {
                this.testDatabase();
                this.testInterpretationCorrection();
                this.testSpecificNumbers();
            }

            testDatabase() {
                const testEl = document.getElementById('database-test');
                const testNumbers = [3, 6, 8, 55, 70];
                let results = '';

                testNumbers.forEach(num => {
                    const info = this.smorfiaService.getNumberInfo(num);
                    if (info) {
                        results += `
                            <div class="test-result success">
                                <span class="number-display">${num}</span>
                                <strong>${info.significato}</strong>
                                <br><small>Simboli: ${info.simboli.join(', ')}</small>
                                <br><small>Descrizione: ${info.descrizione}</small>
                            </div>
                        `;
                    } else {
                        results += `
                            <div class="test-result error">
                                <span class="number-display">${num}</span>
                                ❌ Numero non trovato nel database
                            </div>
                        `;
                    }
                });

                testEl.innerHTML = results;
            }

            testInterpretationCorrection() {
                const testEl = document.getElementById('interpretation-test');
                
                // Simula interpretazioni con errori
                const testCases = [
                    {
                        original: "Il numero 70 (Vecchiaia) rappresenta il distacco dal passato mentre il 3 (Casa) indica sicurezza.",
                        numbers: [70, 3],
                        expected: "Il numero 70 (Il palazzo) rappresenta il distacco dal passato mentre il 3 (La gatta) indica sicurezza."
                    },
                    {
                        original: "Il 55 (Ufficio) simboleggia il lavoro e il 6 (Città) rappresenta l'ambiente urbano.",
                        numbers: [55, 6],
                        expected: "Il 55 (La musica) simboleggia il lavoro e il 6 (Chella che guarda 'nterra) rappresenta l'ambiente urbano."
                    }
                ];

                let results = '';
                testCases.forEach((testCase, index) => {
                    const corrected = this.correctInterpretationMeanings(testCase.original, testCase.numbers);
                    const isCorrect = this.checkCorrection(corrected, testCase.numbers);
                    
                    results += `
                        <div class="test-result ${isCorrect ? 'success' : 'error'}">
                            <h4>Test Case ${index + 1} ${isCorrect ? '✅' : '❌'}</h4>
                            <strong>Originale:</strong>
                            <pre>${testCase.original}</pre>
                            <strong>Corretto:</strong>
                            <pre>${corrected}</pre>
                        </div>
                    `;
                });

                testEl.innerHTML = results;
            }

            testSpecificNumbers() {
                const testEl = document.getElementById('numbers-test');
                const problematicNumbers = [3, 6, 8, 55, 70];
                
                let results = '<h4>Verifica significati corretti:</h4>';
                problematicNumbers.forEach(num => {
                    const info = this.smorfiaService.getNumberInfo(num);
                    results += `
                        <div class="test-result info">
                            <span class="number-display">${num}</span>
                            <strong>${info.significato}</strong>
                            <br><small>${info.descrizione}</small>
                        </div>
                    `;
                });

                testEl.innerHTML = results;
            }

            correctInterpretationMeanings(interpretation, numbers) {
                if (!interpretation || !numbers) return interpretation;
                
                let correctedText = interpretation;
                
                numbers.forEach(numero => {
                    const numberInfo = this.smorfiaService.getNumberInfo(numero);
                    if (numberInfo) {
                        const correctMeaning = numberInfo.significato;
                        
                        const patterns = [
                            new RegExp(`${numero}\\s*\\([^)]+\\)`, 'gi'),
                            new RegExp(`numero\\s+${numero}\\s*\\([^)]+\\)`, 'gi')
                        ];
                        
                        patterns.forEach(pattern => {
                            correctedText = correctedText.replace(pattern, `${numero} (${correctMeaning})`);
                        });
                    }
                });
                
                return correctedText;
            }

            checkCorrection(correctedText, numbers) {
                return numbers.every(num => {
                    const info = this.smorfiaService.getNumberInfo(num);
                    return correctedText.includes(`${num} (${info.significato})`);
                });
            }
        }

        // Avvia i test
        const testSuite = new TestSuite();
        testSuite.init();
    </script>
</body>
</html>
