#!/usr/bin/env node

/**
 * 🔍 Test Modelli Gemini Disponibili
 * Verifica quali modelli funzionano con la tua API key
 */

require('dotenv').config();

// Lista modelli da testare (dal più avanzato al più base)
const MODELS_TO_TEST = [
  'gemini-2.5-pro-preview-05-06',
  'gemini-2.5-pro',
  'gemini-1.5-pro-002',
  'gemini-1.5-pro',
  'gemini-1.5-flash-002',
  'gemini-1.5-flash',
  'gemini-pro'
];

async function testModel(modelName) {
  const testPrompt = {
    contents: [{
      parts: [{
        text: "Rispondi semplicemente: FUNZIONA"
      }]
    }]
  };

  try {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/${modelName}:generateContent?key=${process.env.GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testPrompt)
      }
    );

    if (response.ok) {
      const data = await response.json();
      const responseText = data.candidates[0].content.parts[0].text;
      return { status: 'OK', response: responseText.trim() };
    } else {
      return { status: 'ERROR', error: `HTTP ${response.status}` };
    }
  } catch (error) {
    return { status: 'ERROR', error: error.message };
  }
}

async function findAvailableModels() {
  console.log('🔍 Ricerca Modelli Gemini Disponibili');
  console.log('=====================================');
  console.log('');

  if (!process.env.GEMINI_API_KEY) {
    console.log('❌ API Key non trovata');
    process.exit(1);
  }

  const availableModels = [];
  const unavailableModels = [];

  for (const model of MODELS_TO_TEST) {
    process.stdout.write(`📡 Testing ${model}... `);
    
    const result = await testModel(model);
    
    if (result.status === 'OK') {
      console.log('✅ DISPONIBILE');
      availableModels.push(model);
    } else {
      console.log(`❌ ${result.error}`);
      unavailableModels.push({ model, error: result.error });
    }
  }

  console.log('');
  console.log('📊 RISULTATI:');
  console.log('==============');
  
  if (availableModels.length > 0) {
    console.log('');
    console.log('✅ MODELLI DISPONIBILI:');
    availableModels.forEach((model, index) => {
      const isAdvanced = model.includes('2.5') || model.includes('1.5');
      const emoji = isAdvanced ? '🚀' : '⚡';
      console.log(`   ${index + 1}. ${emoji} ${model}`);
    });

    const bestModel = availableModels[0];
    console.log('');
    console.log(`🏆 MIGLIOR MODELLO DISPONIBILE: ${bestModel}`);
    
    return bestModel;
  } else {
    console.log('❌ Nessun modello disponibile!');
    console.log('   Verifica la tua API key su https://makersuite.google.com');
    process.exit(1);
  }
}

// Test interpretazione sogno con il miglior modello
async function testDreamInterpretation(modelName) {
  console.log('');
  console.log(`🌙 Test Interpretazione Sogno con ${modelName}`);
  console.log('=' .repeat(50 + modelName.length));

  const dreamPrompt = {
    contents: [{
      parts: [{
        text: `Sei un esperto della smorfia napoletana. Interpreta questo sogno: "Ho sognato di volare sopra il mare". Rispondi in JSON con: interpretazione, simboli identificati, e 3 numeri da 1 a 90.`
      }]
    }]
  };

  try {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/${modelName}:generateContent?key=${process.env.GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dreamPrompt)
      }
    );

    const data = await response.json();
    const responseText = data.candidates[0].content.parts[0].text;
    
    console.log('✅ Interpretazione generata!');
    console.log('📝 Anteprima:');
    console.log(responseText.substring(0, 300) + '...');
    
    return true;
  } catch (error) {
    console.log('❌ Errore:', error.message);
    return false;
  }
}

// Main
(async () => {
  try {
    const bestModel = await findAvailableModels();
    const success = await testDreamInterpretation(bestModel);
    
    if (success) {
      console.log('');
      console.log('🎉 CONFIGURAZIONE CONSIGLIATA:');
      console.log('==============================');
      console.log(`Modello da usare: ${bestModel}`);
      console.log('');
      console.log('🔧 Per aggiornare il progetto:');
      console.log(`   Modifica proxy-server.js riga 62:`);
      console.log(`   gemini-pro → ${bestModel}`);
      console.log('');
      console.log('🚀 Riavvia il proxy: npm start');
    }
  } catch (error) {
    console.error('Errore:', error);
    process.exit(1);
  }
})();