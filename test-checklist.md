# 🧪 Checklist Test Manuale - Smorfia Dreams

## Test di Base

### ✅ Caricamento Iniziale
- [ ] L'app si carica senza errori
- [ ] La schermata welcome è visibile
- [ ] I bottoni "<PERSON>rivi" e "Voce" funzionano
- [ ] La navigazione bottom è presente

### ✅ Input Testuale
- [ ] Il bottone "Scrivi il sogno" porta alla schermata corretta
- [ ] Il contatore caratteri funziona (0/1000)
- [ ] Il bottone "Interpreta" si abilita dopo 10 caratteri
- [ ] Il bottone "Indietro" torna alla home

**Test con testo:**
```
Ho sognato di volare sopra il mare blu. C'erano dei pesci dorati che saltavano fuori dall'acqua e una donna vestita di bianco mi sorrideva dal cielo. Ho sentito una grande pace e serenità.
```

### ✅ Input Vocale
- [ ] Il bottone "Racconta a voce" porta alla schermata corretta
- [ ] Richiede permission per il microfono
- [ ] Il bottone recording cambia stato (Inizia/Ferma)
- [ ] Il timer della registrazione funziona
- [ ] La trascrizione appare dopo aver fermato
- [ ] Il bottone "Interpreta" si abilita

### ✅ Processo di Interpretazione
- [ ] Mostra la schermata loading con spinner
- [ ] Gli step di caricamento si attivano in sequenza:
  - 🔍 Analisi del sogno
  - 📚 Consultazione smorfia
  - 🎯 Generazione numeri
- [ ] Passa automaticamente ai risultati

### ✅ Schermata Risultati
- [ ] Mostra 5 numeri in card colorate
- [ ] L'interpretazione è leggibile
- [ ] I simboli sono visualizzati come tag
- [ ] I bottoni "Salva", "Condividi", "Nuovo sogno" funzionano

### ✅ Storico
- [ ] Il bottone "Storico" in bottom nav funziona
- [ ] Mostra "Nessun sogno salvato" se vuoto
- [ ] Dopo aver salvato, mostra la lista
- [ ] Le card dello storico sono leggibili

### ✅ PWA Features
- [ ] Il Service Worker si registra (controlla console)
- [ ] L'app funziona offline (disconnetti internet)
- [ ] Il manifest.json è valido
- [ ] L'app è installabile (pulsante installa browser)

## Test Avanzati

### 🔧 Responsive Design
- [ ] Mobile portrait (320px+)
- [ ] Mobile landscape 
- [ ] Tablet (768px+)
- [ ] Desktop (1024px+)

### 🎨 UI/UX
- [ ] Animazioni fluide
- [ ] Hover effects funzionano
- [ ] Focus states accessibili
- [ ] Contrasti leggibili

### 🚨 Error Handling
- [ ] Testo sogno troppo breve (< 10 char)
- [ ] Microfono non disponibile
- [ ] Connessione internet assente
- [ ] API non risponde

## Test Browser

### ✅ Desktop
- [ ] Chrome (latest)
- [ ] Safari (latest) 
- [ ] Firefox (latest)
- [ ] Edge (latest)

### ✅ Mobile
- [ ] Chrome Mobile
- [ ] Safari iOS
- [ ] Samsung Internet
- [ ] Firefox Mobile

## Simulazione Errori

### Test 1: Sogno Troppo Breve
```
Input: "ciao"
Risultato atteso: Toast "Il sogno deve essere più dettagliato"
```

### Test 2: Microfono Negato
```
Azione: Nega permission microfono
Risultato atteso: Toast "Errore nell'accesso al microfono"
```

### Test 3: Offline
```
Azione: Disconnetti internet + prova interpretazione
Risultato atteso: Toast "Servizio temporaneamente non disponibile"
```

## Performance Check

- [ ] First Contentful Paint < 2s
- [ ] Largest Contentful Paint < 3s
- [ ] Lighthouse PWA score > 90
- [ ] Nessun errore console
- [ ] Memory usage normale (< 50MB)

## Checklist Pre-Deploy

- [ ] Tutti i test passati
- [ ] API key configurata correttamente
- [ ] HTTPS attivo
- [ ] Service Worker aggiornato
- [ ] Manifest.json completo
- [ ] README aggiornato
- [ ] Nessun console.log in produzione

---

**Data test**: ___________  
**Tester**: ___________  
**Browser**: ___________  
**Dispositivo**: ___________  
**Risultato**: ✅ PASS / ❌ FAIL