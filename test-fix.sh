#!/bin/bash
echo "🔧 Test completo sistema Smorfia Dreams"

echo "1. Verifico proxy server..."
curl -s http://localhost:3001/health || echo "❌ Proxy offline - avvia con: node proxy-server.js"

echo "2. Verifico numeri problematici..."
node -e "
const fs = require('fs');
const data = JSON.parse(fs.readFileSync('./data/smorfia.json'));
const numeri = [31, 70, 76, 81];
numeri.forEach(n => {
  if (data.smorfia[n]) {
    console.log('✅', n, ':', data.smorfia[n].significato);
  } else {
    console.log('❌', n, ': Non trovato');
  }
});
"

echo "3. Test completato!"