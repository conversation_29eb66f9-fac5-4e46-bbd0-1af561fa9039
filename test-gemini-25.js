#!/usr/bin/env node

/**
 * 🧪 Test Gemini 2.5 Pro Preview 05-06
 * Verifica se il modello avanzato funziona con la tua API key
 */

require('dotenv').config();

async function testGemini25ProPreview() {
  console.log('🧪 Test Gemini 2.5 Pro Preview 05-06');
  console.log('=====================================');
  console.log('');

  // Test 1: Verifica API key
  if (!process.env.GEMINI_API_KEY) {
    console.log('❌ API Key non trovata nel file .env');
    process.exit(1);
  }
  console.log('✅ API Key trovata');

  // Test 2: Test semplice
  console.log('🔍 Test 2: Verifica connessione modello...');
  
  const testPrompt = {
    contents: [{
      parts: [{
        text: `Test di connessione. Rispondi semplicemente: "Gemini 2.5 Pro Preview 05-06 funziona correttamente!" seguito da un numero casuale da 1 a 90.`
      }]
    }]
  };

  try {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-2.5-pro-preview-05-06:generateContent?key=${process.env.GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testPrompt)
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.candidates[0].content.parts[0].text;
    
    console.log('✅ Connessione riuscita!');
    console.log(`📝 Risposta modello: "${responseText}"`);
    console.log('');

  } catch (error) {
    console.log('❌ Errore connessione modello:');
    console.log(`   ${error.message}`);
    console.log('');
    
    if (error.message.includes('404')) {
      console.log('💡 Possibili cause:');
      console.log('   - Il modello potrebbe non essere disponibile nella tua regione');
      console.log('   - La tua API key potrebbe non avere accesso ai modelli preview');
      console.log('   - Il nome del modello potrebbe essere cambiato');
    } else if (error.message.includes('403')) {
      console.log('💡 La tua API key non ha permessi per questo modello');
      console.log('   Prova con gemini-pro o gemini-1.5-pro');
    }
    
    process.exit(1);
  }

  // Test 3: Test interpretazione sogno
  console.log('🌙 Test 3: Interpretazione sogno avanzata...');
  
  const dreamPrompt = {
    contents: [{
      parts: [{
        text: `Sei un esperto interprete di sogni e della smorfia napoletana.
Analizza questo sogno: "Ho sognato di volare sopra il mare blu con pesci dorati"

Rispondi in formato JSON:
{
  "interpretazione": "Spiegazione dettagliata del significato",
  "simboli": ["simbolo1", "simbolo2", "simbolo3"],
  "numeri_suggeriti": [numero1, numero2, numero3, numero4, numero5],
  "significati_numeri": {
    "numero1": "significato primo numero"
  }
}`
      }]
    }]
  };

  try {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-2.5-pro-preview-05-06:generateContent?key=${process.env.GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(dreamPrompt)
      }
    );

    const data = await response.json();
    const responseText = data.candidates[0].content.parts[0].text;
    
    console.log('✅ Interpretazione generata!');
    console.log('📝 Anteprima risposta:');
    console.log(responseText.substring(0, 200) + '...');
    console.log('');

  } catch (error) {
    console.log('❌ Errore nel test interpretazione:');
    console.log(`   ${error.message}`);
    process.exit(1);
  }

  console.log('🎉 TUTTI I TEST SUPERATI!');
  console.log('========================');
  console.log('');
  console.log('🚀 Il modello Gemini 2.5 Pro Preview 05-06 è OPERATIVO!');
  console.log('   - Interpretazioni più intelligenti e dettagliate');
  console.log('   - Reasoning avanzato per simboli onirici');
  console.log('   - Performance superiore al gemini-pro standard');
  console.log('');
  console.log('💡 Per usarlo nella tua app:');
  console.log('   1. Il proxy server è già configurato');
  console.log('   2. Avvia: npm start');
  console.log('   3. Testa l\'app su: http://localhost:8000');
}

// Avvia il test
testGemini25ProPreview().catch(console.error);