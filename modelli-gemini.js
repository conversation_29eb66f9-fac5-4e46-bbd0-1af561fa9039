/*
 * MODELLI GEMINI DISPONIBILI
 * Modifica la riga 62 in proxy-server.js per cambiare modello
 */

// OPZIONE 1: gemini-pro (Attuale - Bilanciato)
`https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent?key=${process.env.GEMINI_API_KEY}`

// OPZIONE 2: gemini-1.5-pro (<PERSON>ù <PERSON>)
`https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent?key=${process.env.GEMINI_API_KEY}`

// OPZIONE 3: gemini-1.5-flash (<PERSON><PERSON> Veloce)
`https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`

// OPZIONE 4: gemini-pro-vision (Per immagini - Non necessario)
`https://generativelanguage.googleapis.com/v1/models/gemini-pro-vision:generateContent?key=${process.env.GEMINI_API_KEY}`

/*
 * ISTRUZIONI PER CAMBIARE:
 * 1. Apri proxy-server.js
 * 2. Trova la riga 62 con l'URL dell'API
 * 3. Sostituisci "gemini-pro" con il modello desiderato
 * 4. Riavvia il server: npm start
 */