/*
 * CONFIG.JS - Configurazioni dell'applicazione
 * Modifica questo file per personalizzare l'app
 */

const CONFIG = {
  // Configurazione API
  api: {
    // Per sviluppo locale (NON sicuro per produzione)
    geminiApiKey: '', // Inserisci la tua API key qui

    // Per produzione su Netlify (raccomandato)
    // L'endpoint viene determinato automaticamente da AIService
    netlifyFunction: '/.netlify/functions/interpret-dream',
    localProxy: 'http://localhost:3001/api/interpret-dream',

    // Nota: AIService rileva automaticamente l'ambiente e usa l'endpoint corretto

    // Timeout per le chiamate API (ms)
    timeout: 15000, // Aumentato per Netlify Functions

    // Numero massimo di tentativi
    maxRetries: 3
  },

  // Configurazione App
  app: {
    // Nome dell'app (mostrato in vari punti)
    name: 'Smorf-IA',

    // Versione (per cache e aggiornamenti)
    version: '1.0.0',

    // Numero massimo di sogni salvati localmente
    maxSavedDreams: 100,

    // Lunghezza minima sogno (caratteri)
    minDreamLength: 10,

    // Numero massimo caratteri sogno
    maxDreamLength: 1000,

    // Durata massima registrazione vocale (secondi)
    maxRecordingDuration: 180
  },

  // Configurazione UI
  ui: {
    // Durata animazioni (ms)
    animationDuration: 300,

    // Durata toast messages (ms)
    toastDuration: 3000,

    // Tema di default
    theme: 'default', // 'default', 'dark', 'high-contrast'

    // Mostra suggerimenti per nuovi utenti
    showTips: true
  },

  // Configurazione Debug
  debug: {
    // Abilita console.log dettagliati
    enabled: false, // Metti true per sviluppo

    // Simula ritardi API per testing
    simulateDelay: false,

    // Usa dati mock invece delle API reali
    useMockData: false
  }
};