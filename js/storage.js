/* STORAGE.JS - Gestione IndexedDB per salvataggio locale */

export class StorageService {
  constructor() {
    this.dbName = 'SmorfiaDB';
    this.dbVersion = 1;
    this.db = null;
  }

  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ Database locale inizializzato');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('dreams')) {
          const store = db.createObjectStore('dreams', { keyPath: 'id', autoIncrement: true });
          store.createIndex('date', 'date', { unique: false });
        }
      };
    });
  }

  async saveDream(dreamData) {
    if (!this.db) throw new Error('Database non inizializzato');

    const dreamRecord = {
      ...dreamData,
      date: new Date().toISOString(),
      timestamp: Date.now()
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['dreams'], 'readwrite');
      const store = transaction.objectStore('dreams');
      const request = store.add(dreamRecord);

      request.onsuccess = () => {
        console.log('✅ Sogno salvato con ID:', request.result);
        resolve(request.result);
      };
      request.onerror = () => reject(request.error);
    });
  }
  async getAllDreams() {
    if (!this.db) return [];

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['dreams'], 'readonly');
      const store = transaction.objectStore('dreams');
      const request = store.getAll();

      request.onsuccess = () => {
        const dreams = request.result.sort((a, b) => b.timestamp - a.timestamp);
        resolve(dreams);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async deleteDream(id) {
    if (!this.db) return false;

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['dreams'], 'readwrite');
      const store = transaction.objectStore('dreams');
      const request = store.delete(id);

      request.onsuccess = () => resolve(true);
      request.onerror = () => reject(request.error);
    });
  }

  async clearAllDreams() {
    if (!this.db) return false;

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction(['dreams'], 'readwrite');
      const store = transaction.objectStore('dreams');
      const request = store.clear();

      request.onsuccess = () => {
        console.log('✅ Tutti i sogni cancellati');
        resolve(true);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // Utilities
  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}