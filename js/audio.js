/* AUDIO.JS - Gestione registrazione e trascrizione audio */

export class AudioManager {
  constructor() {
    this.isRecording = false;
    this.recognition = null;
    this.transcriptText = '';
    this.setupSpeechRecognition();
  }

  setupSpeechRecognition() {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) return;

    this.recognition = new SpeechRecognition();
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = 'it-IT';

    this.recognition.onresult = (event) => {
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; i++) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        }
      }
      this.transcriptText += finalTranscript;
      this.updateTranscriptionUI();
    };
    this.recognition.onerror = () => this.stopRecording();
  }

  async startRecording() {
    await navigator.mediaDevices.getUserMedia({ audio: true });
    this.transcriptText = '';
    this.recognition.start();
    this.isRecording = true;
    this.updateRecordingUI(true);
  }

  stopRecording() {
    if (this.recognition && this.isRecording) {
      this.recognition.stop();
      this.isRecording = false;
      this.updateRecordingUI(false);
    }
    return this.transcriptText;
  }

  updateRecordingUI(recording) {
    const recordBtn = document.getElementById('record-btn');
    const recordingStatus = document.getElementById('recording-status');
    const visualizer = document.getElementById('voice-visualizer');
    
    if (recording) {
      recordBtn.textContent = 'Ferma registrazione';
      recordBtn.classList.add('recording');
      recordingStatus.classList.remove('hidden');
      visualizer.classList.add('recording');
      this.startTimer();
    } else {
      recordBtn.textContent = 'Inizia registrazione';
      recordBtn.classList.remove('recording');
      recordingStatus.classList.add('hidden');
      visualizer.classList.remove('recording');
      this.stopTimer();
    }
  }

  updateTranscriptionUI() {
    const transcriptionArea = document.getElementById('transcription-area');
    const transcriptionText = document.getElementById('transcription-text');
    const analyzeBtn = document.getElementById('analyze-voice-btn');
    
    if (this.transcriptText.trim()) {
      transcriptionArea.classList.remove('hidden');
      transcriptionText.textContent = this.transcriptText;
      analyzeBtn.disabled = this.transcriptText.length < 10;
    }
  }

  startTimer() {
    const timerElement = document.getElementById('recording-timer');
    let seconds = 0;
    
    this.recordingTimer = setInterval(() => {
      seconds++;
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }, 1000);
  }

  stopTimer() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }
}