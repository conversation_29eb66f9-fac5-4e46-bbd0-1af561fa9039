/*
 * APP.JS - File principale dell'applicazione
 * Gestisce la navigazione, inizializzazione e coordinamento moduli
 * Parte del progetto Smorfia Dreams PWA
 */

// Import dei moduli
import { AudioManager } from './audio.js';
import { AIService } from './ai.js';
import { SmorfiaService } from './smorfia.js';
import { StorageService } from './storage.js';

/**
 * Classe principale dell'applicazione
 */
class App {
  constructor() {
    this.currentScreen = 'welcome-screen';
    this.audioManager = new AudioManager();
    this.aiService = new AIService();
    this.smorfiaService = new SmorfiaService();
    this.storageService = new StorageService();

    // Binding dei metodi per mantenere il contesto
    this.showScreen = this.showScreen.bind(this);
  }

  /**
   * Inizializza l'applicazione
   */
  async init() {
    console.log('🤖 Inizializzazione Smorf-IA...');

    try {
      await this.smorfiaService.init();

      // Passa il database della smorfia all'AI Service
      this.aiService.setSmorfiaDatabase(this.smorfiaService.smorfiaData);

      await this.storageService.init();
      this.setupEventListeners();
      this.showScreen('welcome-screen');
      console.log('✅ App inizializzata con successo');
    } catch (error) {
      console.error('❌ Errore inizializzazione:', error);
      this.showToast('Errore durante l\'inizializzazione', 'error');
    }
  }

  /**
   * Configura tutti gli event listener dell'app
   */
  setupEventListeners() {
    this.setupNavigationListeners();
    this.setupInputChoiceListeners();
    this.setupTextInputListeners();
    this.setupVoiceInputListeners();
    this.setupResultsListeners();
    this.setupBackButtonListeners();
  }

  /**
   * Setup listener per la navigazione bottom
   */
  setupNavigationListeners() {
    const navHome = document.getElementById('nav-home');
    const navHistory = document.getElementById('nav-history');
    const navInfo = document.getElementById('nav-info');

    navHome?.addEventListener('click', () => {
      this.showScreen('welcome-screen');
      this.setActiveNavButton('nav-home');
    });

    navHistory?.addEventListener('click', () => {
      this.showScreen('history-screen');
      this.loadHistory();
      this.setActiveNavButton('nav-history');
    });

    navInfo?.addEventListener('click', () => {
      this.showInfoModal();
    });
  }

  /**
   * Setup listener per i bottoni di scelta input
   */
  setupInputChoiceListeners() {
    const textInputBtn = document.getElementById('text-input-btn');
    const voiceInputBtn = document.getElementById('voice-input-btn');

    textInputBtn?.addEventListener('click', () => {
      this.showScreen('text-input-screen');
    });

    voiceInputBtn?.addEventListener('click', () => {
      this.showScreen('voice-input-screen');
    });
  }

  /**
   * Setup listener per input testuale
   */
  setupTextInputListeners() {
    const dreamText = document.getElementById('dream-text');
    const analyzeBtn = document.getElementById('analyze-text-btn');
    const charCounter = document.getElementById('char-counter');

    dreamText?.addEventListener('input', (e) => {
      const length = e.target.value.length;
      charCounter.textContent = length;
      analyzeBtn.disabled = length < 10;
    });

    analyzeBtn?.addEventListener('click', () => {
      this.analyzeDream(dreamText.value, 'text');
    });
  }
  /**
   * Configura tutti gli event listener dell'app
   */
  setupEventListeners() {
    this.setupNavigationListeners();
    this.setupInputChoiceListeners();
    this.setupTextInputListeners();
    this.setupVoiceInputListeners();
    this.setupResultsListeners();
    this.setupBackButtonListeners();
  }

  setupNavigationListeners() {
    const navHome = document.getElementById('nav-home');
    const navHistory = document.getElementById('nav-history');

    navHome?.addEventListener('click', () => {
      this.showScreen('welcome-screen');
      this.setActiveNavButton('nav-home');
    });

    navHistory?.addEventListener('click', () => {
      this.showScreen('history-screen');
      this.loadHistory();
      this.setActiveNavButton('nav-history');
    });
  }

  setupInputChoiceListeners() {
    const textInputBtn = document.getElementById('text-input-btn');
    const voiceInputBtn = document.getElementById('voice-input-btn');

    textInputBtn?.addEventListener('click', () => {
      this.showScreen('text-input-screen');
    });

    voiceInputBtn?.addEventListener('click', () => {
      this.showScreen('voice-input-screen');
    });
  }

  setupTextInputListeners() {
    const dreamText = document.getElementById('dream-text');
    const analyzeBtn = document.getElementById('analyze-text-btn');
    const charCounter = document.getElementById('char-counter');

    dreamText?.addEventListener('input', (e) => {
      const length = e.target.value.length;
      charCounter.textContent = length;
      analyzeBtn.disabled = length < 10;
    });

    analyzeBtn?.addEventListener('click', () => {
      this.analyzeDream(dreamText.value, 'text');
    });
  }

  setupVoiceInputListeners() {
    const recordBtn = document.getElementById('record-btn');
    const analyzeVoiceBtn = document.getElementById('analyze-voice-btn');

    recordBtn?.addEventListener('click', () => {
      this.toggleRecording();
    });

    analyzeVoiceBtn?.addEventListener('click', () => {
      const transcriptText = this.audioManager.transcriptText;
      this.analyzeDream(transcriptText, 'voice');
    });
  }
  setupResultsListeners() {
    const saveBtn = document.getElementById('save-result-btn');
    const shareBtn = document.getElementById('share-result-btn');
    const newDreamBtn = document.getElementById('new-dream-btn');

    saveBtn?.addEventListener('click', () => {
      this.saveCurrentResult();
    });

    shareBtn?.addEventListener('click', () => {
      this.shareResult();
    });

    newDreamBtn?.addEventListener('click', () => {
      this.showScreen('welcome-screen');
    });
  }

  setupBackButtonListeners() {
    const backButtons = document.querySelectorAll('.back-btn');
    backButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        this.showScreen('welcome-screen');
      });
    });
  }

  /**
   * Mostra una schermata specifica
   */
  showScreen(screenId) {
    // Nascondi tutte le schermate
    const screens = document.querySelectorAll('.screen');
    screens.forEach(screen => {
      screen.classList.remove('active');
    });

    // Mostra la schermata richiesta
    const targetScreen = document.getElementById(screenId);
    if (targetScreen) {
      targetScreen.classList.add('active');
      this.currentScreen = screenId;
    }
  }

  setActiveNavButton(activeId) {
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(btn => {
      btn.classList.remove('active');
    });
    document.getElementById(activeId)?.classList.add('active');
  }

  async toggleRecording() {
    if (this.audioManager.isRecording) {
      const transcript = this.audioManager.stopRecording();
      console.log('🎤 Registrazione fermata, trascrizione:', transcript);
    } else {
      try {
        await this.audioManager.startRecording();
        console.log('🎤 Registrazione avviata');
      } catch (error) {
        this.showToast('Errore nell\'accesso al microfono', 'error');
      }
    }
  }
  /**
   * Funzione principale per analizzare un sogno
   */
  async analyzeDream(dreamText, inputType) {
    if (!dreamText || dreamText.trim().length < 10) {
      this.showToast('Il sogno deve essere più dettagliato', 'warning');
      return;
    }

    this.showScreen('loading-screen');
    this.updateLoadingSteps(1);

    try {
      // Step 1: Interpretazione AI
      console.log('🤖 Invio sogno all\'AI...');
      const aiResult = await this.aiService.interpretDream(dreamText);
      this.updateLoadingSteps(2);

      // Step 2: Matching con smorfia locale
      console.log('📚 Consultazione smorfia locale...');
      const smorfiaMatches = this.smorfiaService.findNumbers(aiResult.simboli);
      this.updateLoadingSteps(3);

      // Step 3: Generazione risultati finali
      console.log('🎯 Generazione numeri finali...');
      const finalResult = this.combineResults(aiResult, smorfiaMatches, dreamText, inputType);

      // Mostra risultati
      setTimeout(() => {
        this.displayResults(finalResult);
        this.showScreen('results-screen');
      }, 1000);

    } catch (error) {
      console.error('❌ Errore analisi sogno:', error);
      this.showToast('Errore nell\'interpretazione del sogno', 'error');
      this.showScreen('welcome-screen');
    }
  }

  /**
   * Combina i risultati AI con la smorfia locale
   */
  combineResults(aiResult, smorfiaMatches, dreamText, inputType) {
    // Combina i numeri suggeriti dall'AI con quelli della smorfia
    const aiNumbers = aiResult.numeri_suggeriti || [];
    const smorfiaNumbers = smorfiaMatches.map(match => match.numero);

    // Rimuovi duplicati e limita a 5 numeri
    const allNumbers = [...new Set([...aiNumbers, ...smorfiaNumbers])];
    const finalNumbers = allNumbers.slice(0, 5);

    // Se non abbiamo abbastanza numeri, aggiungi numeri casuali dalla smorfia
    while (finalNumbers.length < 5) {
      const availableNumbers = this.smorfiaService.getAllNumbers();
      const randomNumber = availableNumbers[Math.floor(Math.random() * availableNumbers.length)];
      if (!finalNumbers.includes(randomNumber)) {
        finalNumbers.push(randomNumber);
      }
    }

    return {
      dreamText: dreamText,
      inputType: inputType,
      interpretation: aiResult.interpretazione,
      symbols: aiResult.simboli,
      numbers: finalNumbers.sort((a, b) => a - b),
      numbersExplanation: aiResult.spiegazione_numeri,
      smorfiaMatches: smorfiaMatches,
      timestamp: Date.now()
    };
  }
  /**
   * Visualizza i risultati nella UI
   */
  displayResults(result) {
    this.currentResult = result;

    // Mostra i numeri
    const numbersContainer = document.getElementById('numbers-container');
    numbersContainer.innerHTML = '';

    result.numbers.forEach(numero => {
      const numberCard = document.createElement('div');
      numberCard.className = 'number-card';

      const numberInfo = this.smorfiaService.getNumberInfo(numero);

      // Usa SEMPRE il significato dal database locale (fonte di verità)
      let significato = 'Numero della smorfia';
      let descrizione = '';

      if (numberInfo && numberInfo.significato) {
        significato = numberInfo.significato;
        descrizione = numberInfo.descrizione || '';
      } else {
        // Fallback per numeri eventualmente mancanti
        console.warn(`⚠️ Numero ${numero} non trovato nel database smorfia`);
      }

      numberCard.innerHTML = `
        <div class="number-value">${numero}</div>
        <div class="number-meaning">${significato}</div>
        ${descrizione ? `<div class="number-description">${descrizione}</div>` : ''}
      `;

      // Aggiungi tooltip con informazioni aggiuntive
      if (numberInfo && numberInfo.simboli) {
        numberCard.title = `Simboli: ${numberInfo.simboli.join(', ')}`;
      }

      numbersContainer.appendChild(numberCard);
    });

    // Mostra interpretazione (corretta se necessario)
    const interpretationEl = document.getElementById('dream-interpretation');
    const correctedInterpretation = this.correctInterpretationMeanings(result.interpretation, result.numbers);
    interpretationEl.textContent = correctedInterpretation;

    // Mostra spiegazione numeri (se disponibile)
    if (result.numbersExplanation) {
      // Cerca se esiste già una sezione spiegazione numeri
      let explanationContainer = document.querySelector('.numbers-explanation-container');
      if (!explanationContainer) {
        // Crea la sezione se non esiste
        explanationContainer = document.createElement('div');
        explanationContainer.className = 'numbers-explanation-container';
        explanationContainer.innerHTML = `
          <h3>🎯 Perché questi numeri?</h3>
          <div id="numbers-explanation" class="numbers-explanation-text"></div>
        `;
        // Inserisce dopo l'interpretazione
        const interpretationContainer = document.querySelector('.interpretation-container');
        interpretationContainer.parentNode.insertBefore(explanationContainer, interpretationContainer.nextSibling);
      }

      const explanationEl = document.getElementById('numbers-explanation');
      explanationEl.textContent = result.numbersExplanation;
    }

    // Mostra simboli
    const symbolsList = document.getElementById('symbols-list');
    symbolsList.innerHTML = '';

    result.symbols.forEach(symbol => {
      const symbolEl = document.createElement('span');
      symbolEl.className = 'symbol-tag';
      symbolEl.textContent = symbol;
      symbolsList.appendChild(symbolEl);
    });
  }

  /**
   * Corregge i significati nell'interpretazione usando il database locale
   */
  correctInterpretationMeanings(interpretation, numbers) {
    if (!interpretation || !numbers) return interpretation;

    let correctedText = interpretation;

    // Per ogni numero nell'interpretazione, verifica e correggi il significato
    numbers.forEach(numero => {
      const numberInfo = this.smorfiaService.getNumberInfo(numero);
      if (numberInfo) {
        const correctMeaning = numberInfo.significato;

        // Pattern per trovare riferimenti al numero con significato sbagliato
        // Es: "70 (Vecchiaia)" o "numero 70 (Auto precedente)"
        const patterns = [
          new RegExp(`${numero}\\s*\\([^)]+\\)`, 'gi'),
          new RegExp(`numero\\s+${numero}\\s*\\([^)]+\\)`, 'gi')
        ];

        patterns.forEach(pattern => {
          correctedText = correctedText.replace(pattern, `${numero} (${correctMeaning})`);
        });
      }
    });

    // Log per debug
    if (correctedText !== interpretation) {
      console.log('🔧 Interpretazione corretta:', {
        originale: interpretation.substring(0, 100) + '...',
        corretta: correctedText.substring(0, 100) + '...'
      });
    }

    return correctedText;
  }

  /**
   * Aggiorna gli step del loading
   */
  updateLoadingSteps(activeStep) {
    for (let i = 1; i <= 3; i++) {
      const step = document.getElementById(`step-${i}`);
      if (step) {
        if (i <= activeStep) {
          step.classList.add('active');
        } else {
          step.classList.remove('active');
        }
      }
    }
  }

  /**
   * Salva il risultato corrente
   */
  async saveCurrentResult() {
    if (!this.currentResult) return;

    try {
      await this.storageService.saveDream(this.currentResult);
      this.showToast('Sogno salvato con successo!', 'success');
    } catch (error) {
      console.error('❌ Errore salvataggio:', error);
      this.showToast('Errore nel salvataggio', 'error');
    }
  }
  /**
   * Condivide il risultato
   */
  async shareResult() {
    if (!this.currentResult) return;

    const shareText = `🤖 Ho interpretato il mio sogno con Smorf-IA!\n\nNumeri fortunati: ${this.currentResult.numbers.join(', ')}\n\nInterpretazione: ${this.currentResult.interpretation.substring(0, 100)}...`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'I miei numeri fortunati',
          text: shareText
        });
      } catch (error) {
        this.copyToClipboard(shareText);
      }
    } else {
      this.copyToClipboard(shareText);
    }
  }

  /**
   * Copia testo negli appunti
   */
  async copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text);
      this.showToast('Copiato negli appunti!', 'success');
    } catch (error) {
      this.showToast('Impossibile copiare', 'error');
    }
  }

  /**
   * Carica lo storico dei sogni
   */
  async loadHistory() {
    try {
      const dreams = await this.storageService.getAllDreams();
      const historyList = document.getElementById('history-list');
      const historyEmpty = document.getElementById('history-empty');

      if (dreams.length === 0) {
        historyList.innerHTML = '';
        historyEmpty.classList.remove('hidden');
      } else {
        historyEmpty.classList.add('hidden');
        historyList.innerHTML = '';

        dreams.forEach(dream => {
          const dreamCard = this.createHistoryCard(dream);
          historyList.appendChild(dreamCard);
        });
      }
    } catch (error) {
      console.error('❌ Errore caricamento storico:', error);
      this.showToast('Errore nel caricamento dello storico', 'error');
    }
  }
  /**
   * Crea una card per lo storico
   */
  createHistoryCard(dream) {
    const card = document.createElement('div');
    card.className = 'history-card';

    const date = new Date(dream.date).toLocaleDateString('it-IT', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    card.innerHTML = `
      <div class="history-header">
        <span class="history-date">${date}</span>
        <span class="history-type">${dream.inputType === 'voice' ? '🎤' : '✍️'}</span>
      </div>
      <div class="history-dream">${dream.dreamText.substring(0, 100)}...</div>
      <div class="history-numbers">
        Numeri: ${dream.numbers.join(', ')}
      </div>
    `;

    return card;
  }

  /**
   * Mostra un messaggio toast
   */
  showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    const container = document.getElementById('toast-container');
    container.appendChild(toast);

    // Animazione di entrata
    setTimeout(() => toast.classList.add('show'), 100);

    // Rimozione automatica dopo 3 secondi
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => container.removeChild(toast), 300);
    }, 3000);
  }
}

// Inizializzazione app quando il DOM è caricato
document.addEventListener('DOMContentLoaded', () => {
  console.log('🤖 Inizializzazione Smorf-IA...');
  const app = new App();
  app.init();

  // Esponi l'app globalmente per debug
  window.smorfiaApp = app;
});