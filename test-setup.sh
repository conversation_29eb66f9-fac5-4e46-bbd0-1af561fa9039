#!/bin/bash

# 🧪 Script di Test Automatico - Smorfia Dreams
# Verifica che tutti i componenti siano configurati correttamente

echo "🌙 Test Configurazione Smorfia Dreams"
echo "===================================="

# Test 1: Verifica file .env
echo "📋 Test 1: Configurazione API Key..."
if [ -f ".env" ]; then
    if grep -q "GEMINI_API_KEY=AIzaSy" .env; then
        echo "✅ API Key configurata correttamente"
    else
        echo "❌ API Key non trovata nel file .env"
        exit 1
    fi
else
    echo "❌ File .env non trovato"
    exit 1
fi

# Test 2: Verifica dipendenze Node.js
echo "📦 Test 2: Dipendenze Node.js..."
if [ -f "package.json" ]; then
    echo "✅ package.json presente"
    if [ -d "node_modules" ]; then
        echo "✅ Dipendenze già installate"
    else
        echo "⚠️  Dipendenze non installate. Esegui: npm install"
    fi
else
    echo "❌ package.json non trovato"
    exit 1
fi

# Test 3: Verifica struttura file
echo "📁 Test 3: Struttura progetto..."
required_files=("index.html" "manifest.json" "sw.js" "js/app.js" "js/ai.js" "data/smorfia.json")
all_present=true

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file mancante"
        all_present=false
    fi
done

if [ "$all_present" = true ]; then
    echo "✅ Tutti i file necessari sono presenti"
else
    echo "❌ Alcuni file sono mancanti"
    exit 1
fi

# Test 4: Verifica configurazione proxy
echo "🔗 Test 4: Configurazione proxy..."
if grep -q "localhost:3001" js/ai.js; then
    echo "✅ Proxy endpoint configurato"
else
    echo "❌ Proxy endpoint non configurato"
    exit 1
fi

# Test 5: Controllo sicurezza
echo "🔒 Test 5: Sicurezza..."
if grep -q "AIzaSy" js/ai.js; then
    echo "❌ ATTENZIONE: API Key trovata nel frontend! Rimuovi immediatamente!"
    exit 1
else
    echo "✅ API Key non esposta nel frontend"
fi

if grep -q ".env" .gitignore; then
    echo "✅ File .env protetto da Git"
else
    echo "❌ File .env non protetto!"
    exit 1
fi

echo ""
echo "🎉 TUTTI I TEST SUPERATI!"
echo "========================"
echo ""
echo "🚀 Per avviare l'applicazione:"
echo "   Terminal 1: npm start          (porta 3001)"
echo "   Terminal 2: python -m http.server 8000"
echo "   Browser: http://localhost:8000"
echo ""
echo "✨ Il tuo progetto Smorfia Dreams è pronto!"