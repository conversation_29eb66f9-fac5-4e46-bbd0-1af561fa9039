<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Funzione Netlify - Smorfia Dreams</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .status { font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 Test Funzione Netlify - Smorfia Dreams</h1>
    
    <div class="test-section info">
        <h2>📋 Informazioni</h2>
        <p><strong>URL Sito:</strong> <span id="siteUrl"></span></p>
        <p><strong>Endpoint API:</strong> <span id="apiEndpoint"></span></p>
        <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
    </div>

    <div class="test-section">
        <h2>🧪 Test 1: Verifica Endpoint</h2>
        <button onclick="testEndpoint()">Testa Endpoint</button>
        <div id="endpointResult" class="status"></div>
        <pre id="endpointDetails"></pre>
    </div>

    <div class="test-section">
        <h2>🤖 Test 2: Chiamata API Completa</h2>
        <button onclick="testFullAPI()">Testa API Gemini</button>
        <div id="apiResult" class="status"></div>
        <pre id="apiDetails"></pre>
    </div>

    <div class="test-section">
        <h2>🔍 Test 3: Verifica CORS</h2>
        <button onclick="testCORS()">Testa CORS</button>
        <div id="corsResult" class="status"></div>
        <pre id="corsDetails"></pre>
    </div>

    <div class="test-section">
        <h2>📊 Risultati</h2>
        <div id="summary"></div>
    </div>

    <script>
        // Inizializzazione
        document.getElementById('siteUrl').textContent = window.location.origin;
        document.getElementById('apiEndpoint').textContent = '/.netlify/functions/interpret-dream';
        document.getElementById('timestamp').textContent = new Date().toLocaleString();

        let testResults = {};

        // Test 1: Verifica endpoint
        async function testEndpoint() {
            const resultDiv = document.getElementById('endpointResult');
            const detailsDiv = document.getElementById('endpointDetails');
            
            resultDiv.textContent = '🔄 Testing...';
            
            try {
                const response = await fetch('/.netlify/functions/interpret-dream', {
                    method: 'OPTIONS'
                });
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                };
                
                if (response.status === 200) {
                    resultDiv.textContent = '✅ Endpoint raggiungibile';
                    resultDiv.className = 'status success';
                    testResults.endpoint = true;
                } else {
                    resultDiv.textContent = `❌ Endpoint non raggiungibile (${response.status})`;
                    resultDiv.className = 'status error';
                    testResults.endpoint = false;
                }
                
                detailsDiv.textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                resultDiv.textContent = `❌ Errore: ${error.message}`;
                resultDiv.className = 'status error';
                detailsDiv.textContent = error.stack;
                testResults.endpoint = false;
            }
            
            updateSummary();
        }

        // Test 2: API completa
        async function testFullAPI() {
            const resultDiv = document.getElementById('apiResult');
            const detailsDiv = document.getElementById('apiDetails');
            
            resultDiv.textContent = '🔄 Testing API...';
            
            const testPayload = {
                contents: [{
                    parts: [{
                        text: 'Test sogno: ho sognato di volare sopra il mare'
                    }]
                }]
            };
            
            try {
                const response = await fetch('/.netlify/functions/interpret-dream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testPayload)
                });
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch {
                    responseData = responseText;
                }
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: responseData
                };
                
                if (response.status === 200) {
                    resultDiv.textContent = '✅ API funziona correttamente';
                    resultDiv.className = 'status success';
                    testResults.api = true;
                } else {
                    resultDiv.textContent = `❌ API non funziona (${response.status})`;
                    resultDiv.className = 'status error';
                    testResults.api = false;
                }
                
                detailsDiv.textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                resultDiv.textContent = `❌ Errore API: ${error.message}`;
                resultDiv.className = 'status error';
                detailsDiv.textContent = error.stack;
                testResults.api = false;
            }
            
            updateSummary();
        }

        // Test 3: CORS
        async function testCORS() {
            const resultDiv = document.getElementById('corsResult');
            const detailsDiv = document.getElementById('corsDetails');
            
            resultDiv.textContent = '🔄 Testing CORS...';
            
            try {
                const response = await fetch('/.netlify/functions/interpret-dream', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                const corsHeaders = {
                    'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
                    'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
                    'access-control-allow-headers': response.headers.get('access-control-allow-headers')
                };
                
                if (corsHeaders['access-control-allow-origin']) {
                    resultDiv.textContent = '✅ CORS configurato correttamente';
                    resultDiv.className = 'status success';
                    testResults.cors = true;
                } else {
                    resultDiv.textContent = '❌ CORS non configurato';
                    resultDiv.className = 'status error';
                    testResults.cors = false;
                }
                
                detailsDiv.textContent = JSON.stringify(corsHeaders, null, 2);
                
            } catch (error) {
                resultDiv.textContent = `❌ Errore CORS: ${error.message}`;
                resultDiv.className = 'status error';
                detailsDiv.textContent = error.stack;
                testResults.cors = false;
            }
            
            updateSummary();
        }

        // Aggiorna riassunto
        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(Boolean).length;
            
            if (total === 0) return;
            
            summaryDiv.innerHTML = `
                <h3>📊 Riassunto Test</h3>
                <p><strong>Test superati:</strong> ${passed}/${total}</p>
                <ul>
                    <li>Endpoint: ${testResults.endpoint ? '✅' : '❌'}</li>
                    <li>API: ${testResults.api ? '✅' : '❌'}</li>
                    <li>CORS: ${testResults.cors ? '✅' : '❌'}</li>
                </ul>
                ${passed === total ? 
                    '<p class="success">🎉 Tutti i test superati! L\'app dovrebbe funzionare correttamente.</p>' :
                    '<p class="error">⚠️ Alcuni test falliti. Controlla la configurazione su Netlify.</p>'
                }
            `;
        }

        // Auto-test all'avvio
        window.addEventListener('load', () => {
            setTimeout(() => {
                testEndpoint();
                setTimeout(() => testCORS(), 1000);
            }, 500);
        });
    </script>
</body>
</html>
