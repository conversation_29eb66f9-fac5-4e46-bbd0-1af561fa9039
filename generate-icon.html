<!DOCTYPE html>
<html>
<head>
    <title>Generate 192x192 PNG Icon</title>
</head>
<body>
    <h1>Generate Smorf-IA 192x192 PNG Icon</h1>
    <canvas id="canvas" width="192" height="192" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="generateIcon()">Generate Icon</button>
    <button onclick="downloadIcon()">Download PNG</button>
    <br><br>
    <div id="output"></div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        function generateIcon() {
            // Clear canvas
            ctx.clearRect(0, 0, 192, 192);
            
            // Create gradient background
            const gradient = ctx.createRadialGradient(96, 96, 0, 96, 96, 96);
            gradient.addColorStop(0, '#1a365d');
            gradient.addColorStop(1, '#0f2a44');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(96, 96, 96, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw decorative border
            ctx.strokeStyle = '#d69e2e';
            ctx.lineWidth = 2;
            ctx.globalAlpha = 0.3;
            ctx.beginPath();
            ctx.arc(96, 96, 88, 0, 2 * Math.PI);
            ctx.stroke();
            ctx.globalAlpha = 1;
            
            // Draw moon emoji (simplified as circle)
            const moonGradient = ctx.createRadialGradient(96, 96, 0, 96, 96, 30);
            moonGradient.addColorStop(0, '#ffd700');
            moonGradient.addColorStop(1, '#d69e2e');
            
            ctx.fillStyle = moonGradient;
            ctx.beginPath();
            ctx.arc(96, 96, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add crescent shape for moon
            ctx.fillStyle = '#1a365d';
            ctx.beginPath();
            ctx.arc(106, 86, 25, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add stars
            ctx.fillStyle = '#ffd700';
            ctx.globalAlpha = 0.8;
            
            // Star 1
            drawStar(140, 60, 8);
            ctx.globalAlpha = 0.6;
            drawStar(52, 50, 6);
            ctx.globalAlpha = 0.7;
            drawStar(150, 140, 7);
            
            ctx.globalAlpha = 1;
            
            // Add text
            ctx.fillStyle = '#ffd700';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.globalAlpha = 0.9;
            ctx.fillText('Smorf-IA', 96, 170);
            ctx.globalAlpha = 1;
        }
        
        function drawStar(x, y, size) {
            ctx.save();
            ctx.translate(x, y);
            ctx.beginPath();
            for (let i = 0; i < 5; i++) {
                ctx.lineTo(Math.cos((18 + i * 72) / 180 * Math.PI) * size, 
                          Math.sin((18 + i * 72) / 180 * Math.PI) * size);
                ctx.lineTo(Math.cos((54 + i * 72) / 180 * Math.PI) * size * 0.5, 
                          Math.sin((54 + i * 72) / 180 * Math.PI) * size * 0.5);
            }
            ctx.closePath();
            ctx.fill();
            ctx.restore();
        }
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon-192.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Generate icon on load
        generateIcon();
    </script>
</body>
</html>
