/*
 * PROXY SERVER per API Google Gemini
 * Nasconde la API key dal frontend per sicurezza
 * 
 * Installazione:
 * npm init -y
 * npm install express cors dotenv helmet express-rate-limit
 * 
 * File .env:
 * GEMINI_API_KEY=la-tua-api-key-qui
 * PORT=3001
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware di sicurezza
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:8000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));

// Rate limiting con configurazione da .env
const limiter = rateLimit({
  windowMs: process.env.RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000, // 15 minuti default
  max: process.env.RATE_LIMIT_MAX_REQUESTS || 100, // 100 richieste default
  message: { error: 'Troppe richieste, riprova più tardi' }
});
app.use('/api/', limiter);

// Middleware di validazione
const validateRequest = (req, res, next) => {
  const { contents } = req.body;
  
  if (!contents || !Array.isArray(contents)) {
    return res.status(400).json({ error: 'Formato richiesta non valido' });
  }
  
  if (!contents[0]?.parts?.[0]?.text) {
    return res.status(400).json({ error: 'Testo mancante' });
  }
  
  const text = contents[0].parts[0].text;
  if (text.length < 10 || text.length > 5000) {
    return res.status(400).json({ error: 'Lunghezza testo non valida' });
  }
  
  next();
};

// Endpoint per interpretazione sogni
app.post('/api/interpret-dream', validateRequest, async (req, res) => {
  try {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash-002:generateContent?key=${process.env.GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(req.body)
      }
    );

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    const data = await response.json();
    res.json(data);
    
  } catch (error) {
    console.error('Errore API Gemini:', error);
    res.status(500).json({ 
      error: 'Servizio temporaneamente non disponibile' 
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`🚀 Proxy server in esecuzione su http://localhost:${PORT}`);
});