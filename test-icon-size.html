<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dimensioni Icone - Smorf-IA</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .icon-test { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .icon-display { display: flex; align-items: center; gap: 20px; margin: 10px 0; }
        .icon-info { background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        img { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>🔍 Test Dimensioni Icone PWA</h1>
    
    <div class="icon-test">
        <h2>📱 Icona 144x144 (Vecchia)</h2>
        <div class="icon-display">
            <img src="assets/icon-144.png" alt="Icona 144x144 vecchia" id="icon144old">
            <div>
                <strong>File:</strong> assets/icon-144.png<br>
                <strong>Dimensioni attese:</strong> 144x144<br>
                <strong>Dimensioni reali:</strong> <span id="size144old">Caricamento...</span>
            </div>
        </div>
        <div class="icon-info" id="info144old">Verificando dimensioni...</div>
    </div>

    <div class="icon-test">
        <h2>📱 Icona 144x144 (Nuova)</h2>
        <div class="icon-display">
            <img src="assets/icon-144-new.png" alt="Icona 144x144 nuova" id="icon144new">
            <div>
                <strong>File:</strong> assets/icon-144-new.png<br>
                <strong>Dimensioni attese:</strong> 144x144<br>
                <strong>Dimensioni reali:</strong> <span id="size144new">Caricamento...</span>
            </div>
        </div>
        <div class="icon-info" id="info144new">Verificando dimensioni...</div>
    </div>

    <div class="icon-test">
        <h2>📱 Icona 192x192</h2>
        <div class="icon-display">
            <img src="assets/icon-192.png" alt="Icona 192x192" id="icon192">
            <div>
                <strong>File:</strong> assets/icon-192.png<br>
                <strong>Dimensioni attese:</strong> 192x192<br>
                <strong>Dimensioni reali:</strong> <span id="size192">Caricamento...</span>
            </div>
        </div>
        <div class="icon-info" id="info192">Verificando dimensioni...</div>
    </div>

    <div class="icon-test">
        <h2>📋 Manifest PWA</h2>
        <button onclick="testManifest()">🔍 Verifica Manifest</button>
        <div id="manifestResult" class="icon-info">Clicca per verificare il manifest</div>
    </div>

    <script>
        // Funzione per verificare le dimensioni di un'immagine
        function checkImageSize(imgId, sizeId, infoId, expectedWidth, expectedHeight) {
            const img = document.getElementById(imgId);
            const sizeSpan = document.getElementById(sizeId);
            const infoDiv = document.getElementById(infoId);
            
            img.onload = function() {
                const actualWidth = this.naturalWidth;
                const actualHeight = this.naturalHeight;
                
                sizeSpan.textContent = `${actualWidth}x${actualHeight}`;
                
                if (actualWidth === expectedWidth && actualHeight === expectedHeight) {
                    infoDiv.textContent = `✅ Dimensioni corrette: ${actualWidth}x${actualHeight}`;
                    infoDiv.className = 'icon-info success';
                } else {
                    infoDiv.textContent = `❌ Dimensioni errate: ${actualWidth}x${actualHeight} (attese: ${expectedWidth}x${expectedHeight})`;
                    infoDiv.className = 'icon-info error';
                }
            };
            
            img.onerror = function() {
                sizeSpan.textContent = 'Errore caricamento';
                infoDiv.textContent = '❌ Impossibile caricare l\'immagine';
                infoDiv.className = 'icon-info error';
            };
        }

        // Verifica manifest
        async function testManifest() {
            const resultDiv = document.getElementById('manifestResult');
            resultDiv.textContent = '🔄 Verificando manifest...';
            
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                
                let result = '📋 Icone nel manifest:\n\n';
                manifest.icons.forEach((icon, index) => {
                    result += `${index + 1}. ${icon.src}\n`;
                    result += `   Dimensioni: ${icon.sizes}\n`;
                    result += `   Tipo: ${icon.type}\n`;
                    result += `   Scopo: ${icon.purpose || 'any'}\n\n`;
                });
                
                resultDiv.innerHTML = `<pre>${result}</pre>`;
                resultDiv.className = 'icon-info success';
                
            } catch (error) {
                resultDiv.textContent = `❌ Errore nel caricamento del manifest: ${error.message}`;
                resultDiv.className = 'icon-info error';
            }
        }

        // Avvia i test quando la pagina è caricata
        window.addEventListener('load', () => {
            checkImageSize('icon144old', 'size144old', 'info144old', 144, 144);
            checkImageSize('icon144new', 'size144new', 'info144new', 144, 144);
            checkImageSize('icon192', 'size192', 'info192', 192, 192);
        });
    </script>
</body>
</html>
