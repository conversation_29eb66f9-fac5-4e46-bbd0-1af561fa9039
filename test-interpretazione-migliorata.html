<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interpretazione AI Migliorata</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #764ba2;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            resize: vertical;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .results {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .error {
            background: #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #fdcb6e;
            margin: 10px 0;
        }
        .numero-box {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            margin: 5px;
            font-weight: bold;
        }
        .interpretazione {
            line-height: 1.6;
            font-size: 16px;
            text-align: justify;
        }
        .loading {
            text-align: center;
            color: #667eea;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔮 Test Interpretazione AI Migliorata</h1>
        
        <div class="test-section">
            <h3>Inserisci il tuo sogno per testare il nuovo sistema:</h3>
            <textarea id="dreamInput" placeholder="Descrivi il tuo sogno qui... (minimo 10 caratteri)

Esempi di sogni da testare:
- Sognavo di volare sopra il mare con mia madre mentre un gatto nero mi seguiva
- Ho sognato di essere in una chiesa grande e buia dove un vecchio mi dava dei soldi
- Nel sogno cadevo da un palazzo mentre dei soldati ridevano e una donna piangeva"></textarea>
            
            <button onclick="testInterpretation()">🎯 Testa Interpretazione AI</button>
            <button onclick="testFallback()">🔄 Testa Modalità Fallback</button>
            <button onclick="loadSampleDreams()">📝 Carica Sogni di Esempio</button>
        </div>

        <div id="results"></div>
        
        <div class="test-section">
            <h3>📊 Esempi di Sogni per Test Approfonditi:</h3>
            <button onclick="testSample('volare')">Test: Sogno di Volo</button>
            <button onclick="testSample('mare')">Test: Sogno del Mare</button>
            <button onclick="testSample('morte')">Test: Sogno di Morte</button>
            <button onclick="testSample('complesso')">Test: Sogno Complesso</button>
        </div>
    </div>

    <script type="module">
        import { AIService } from './js/ai.js';

        let aiService;
        let smorfiaData;

        // Inizializzazione
        async function init() {
            try {
                // Carica il database smorfia
                const response = await fetch('./data/smorfia.json');
                const data = await response.json();
                smorfiaData = data.smorfia;
                
                // Inizializza AI service
                aiService = new AIService();
                aiService.setSmorfiaDatabase(smorfiaData);
                
                console.log('✅ Sistema inizializzato correttamente');
            } catch (error) {
                console.error('❌ Errore inizializzazione:', error);
                showError('Errore nel caricamento del sistema');
            }
        }

        // Test interpretazione con AI
        window.testInterpretation = async function() {
            const dreamText = document.getElementById('dreamInput').value.trim();
            
            if (dreamText.length < 10) {
                showError('Il sogno deve essere di almeno 10 caratteri');
                return;
            }

            showLoading('Analizzando il sogno con AI Gemini...');

            try {
                const result = await aiService.interpretDream(dreamText);
                showResults(result, 'AI Gemini');
            } catch (error) {
                console.error('Errore interpretazione:', error);
                showError('Errore durante l\'interpretazione: ' + error.message);
            }
        };

        // Test modalità fallback
        window.testFallback = async function() {
            const dreamText = document.getElementById('dreamInput').value.trim();
            
            if (dreamText.length < 10) {
                showError('Il sogno deve essere di almeno 10 caratteri');
                return;
            }

            showLoading('Analizzando il sogno con modalità fallback...');

            try {
                const prompt = aiService.createSmorfiaPrompt(dreamText);
                const fallbackResponse = aiService.generateFallbackResponse(prompt);
                const result = aiService.parseAIResponse(fallbackResponse);
                showResults(result, 'Modalità Fallback');
            } catch (error) {
                console.error('Errore fallback:', error);
                showError('Errore durante l\'interpretazione fallback: ' + error.message);
            }
        };

        // Carica sogni di esempio
        window.loadSampleDreams = function() {
            const samples = [
                "Sognavo di volare sopra il mare cristallino insieme a mia madre, mentre un gatto nero elegante ci seguiva dall'alto. Il vento era caldo e sentivo una grande pace.",
                "Ho sognato di essere in una chiesa immensa e buia, con alte colonne di marmo. Un vecchio vestito di bianco mi ha dato una borsa piena di monete d'oro scintillanti.",
                "Nel sogno cadevo lentamente da un palazzo altissimo mentre dei soldati in uniforme ridevano rumorosamente. Una donna vestita di nero piangeva e mi tendeva le braccia."
            ];
            
            document.getElementById('dreamInput').value = samples[Math.floor(Math.random() * samples.length)];
        };

        // Test con sogni predefiniti
        window.testSample = async function(type) {
            const samples = {
                'volare': "Sognavo di volare libero come un uccello sopra campi verdi e case bianche, sentendo il vento tra i capelli e una sensazione di libertà assoluta nel cuore.",
                'mare': "Nel sogno camminavo sulla riva di un mare tempestoso di notte, mentre le onde si infrangevano violentemente sugli scogli e la luna piena illuminava tutto di argento.",
                'morte': "Ho sognato il funerale di una persona cara, con tante persone vestite di nero che piangevano, mentre io parlavo con il defunto che sorrideva serenamente.",
                'complesso': "Sognavo di essere in una bottega antica dove un monaco vendeva pane fresco. All'improvviso sono arrivate delle guardie che hanno arrestato tutti, mentre io scappavo attraverso un giardino pieno di fiori profumati verso una fontana magica."
            };

            document.getElementById('dreamInput').value = samples[type];
            await testInterpretation();
        };

        // Funzioni di utilità
        function showLoading(message) {
            document.getElementById('results').innerHTML = `
                <div class="loading">
                    <h3>🔄 ${message}</h3>
                    <p>Attendere prego...</p>
                </div>
            `;
        }

        function showResults(result, source) {
            const numeriHtml = result.numeri_suggeriti.map(num => {
                const significato = smorfiaData[num] ? smorfiaData[num].significato : '';
                return `<span class="numero-box">${num} - ${significato}</span>`;
            }).join('');

            document.getElementById('results').innerHTML = `
                <div class="results">
                    <h3>🎯 Interpretazione da ${source}</h3>
                    
                    <h4>🔢 Numeri Suggeriti:</h4>
                    <div>${numeriHtml}</div>
                    
                    <h4>📖 Interpretazione Completa:</h4>
                    <div class="interpretazione">${result.interpretazione}</div>
                    
                    <h4>🔍 Simboli Identificati:</h4>
                    <p><strong>${result.simboli.join(', ')}</strong></p>
                    
                    <h4>💡 Spiegazione Numeri:</h4>
                    <p>${result.spiegazione_numeri}</p>
                    
                    <h4>📊 Significati Tradizionali:</h4>
                    <ul>
                        ${Object.entries(result.significati_numeri || {}).map(([num, significato]) => 
                            `<li><strong>${num}</strong>: ${significato}</li>`
                        ).join('')}
                    </ul>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <h3>⚠️ Errore</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        // Inizializza all'avvio
        init();
    </script>
</body>
</html>