<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Visual Enhancements - Smorf-IA</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/enhancements.css">
    <link rel="stylesheet" href="css/stock-images.css">

    <style>
        .test-section {
            margin: var(--spacing-8) 0;
            padding: var(--spacing-6);
            background: var(--cream);
            border-radius: var(--radius-lg);
            border: 1px solid var(--warm-200);
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
            margin: var(--spacing-4) 0;
        }

        .demo-number-card {
            background: var(--gradient-primary);
            color: var(--white);
            padding: var(--spacing-4);
            border-radius: var(--radius-xl);
            text-align: center;
            box-shadow: var(--shadow-md);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
        }

        .demo-number-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: var(--shadow-xl);
        }
    </style>
</head>
<body>
    <!-- Header Test -->
    <header class="app-header">
        <div class="container">
            <h1 class="app-title">🔮 Smorf-IA</h1>
            <p class="app-subtitle">Test delle migliorie visive</p>
        </div>
    </header>

    <main class="main-content">
        <div class="container">

            <!-- Typography Test -->
            <section class="test-section">
                <h2 class="neapolitan-title">Test Tipografia</h2>
                <p class="mystical-text">Questo è un testo mistico con font Crimson Text in corsivo.</p>
                <p>Questo è un testo normale con font Inter per la leggibilità.</p>
                <p class="gradient-text">Questo è un testo con gradiente colorato.</p>
                <p class="shadow-text">Questo è un testo con ombra elegante.</p>
            </section>

            <!-- Icons and Decorations Test -->
            <section class="test-section">
                <h2 class="neapolitan-title">Test Icone e Decorazioni</h2>
                <div class="test-grid">
                    <div class="mystical-icon">🔮 Icona Mistica</div>
                    <div class="mystical-star">⭐ Stella Mistica</div>
                    <div class="animate-float">🌙 Animazione Float</div>
                    <div class="animate-mystical-glow">✨ Bagliore Mistico</div>
                </div>
                <div class="decorative-divider"></div>
            </section>

            <!-- Buttons Test -->
            <section class="test-section">
                <h2 class="neapolitan-title">Test Bottoni</h2>
                <div class="test-grid">
                    <button class="primary-btn magic-button">Bottone Magico</button>
                    <button class="secondary-btn shimmer-effect">Bottone Shimmer</button>
                    <button class="choice-btn">
                        <span class="btn-icon mystical-icon">📝</span>
                        <span class="btn-text">Bottone Scelta</span>
                    </button>
                </div>
            </section>

            <!-- Number Cards Test -->
            <section class="test-section">
                <h2 class="neapolitan-title">Test Carte Numeri</h2>
                <div class="test-grid">
                    <div class="demo-number-card number-reveal">
                        <div class="number-value">42</div>
                        <div class="number-meaning">Il caffè</div>
                        <div class="number-description">Il caffè napoletano</div>
                    </div>
                    <div class="demo-number-card number-reveal">
                        <div class="number-value">75</div>
                        <div class="number-meaning">Pulcinella</div>
                        <div class="number-description">Maschera napoletana</div>
                    </div>
                    <div class="demo-number-card number-reveal">
                        <div class="number-value">90</div>
                        <div class="number-meaning">La paura</div>
                        <div class="number-description">Terrore e spavento</div>
                    </div>
                </div>
            </section>

            <!-- Symbols Test -->
            <section class="test-section">
                <h2 class="neapolitan-title">Test Simboli</h2>
                <div class="symbols-list">
                    <span class="symbol-tag">sogno</span>
                    <span class="symbol-tag">mare</span>
                    <span class="symbol-tag">volare</span>
                    <span class="symbol-tag">fortuna</span>
                    <span class="symbol-tag">mistero</span>
                </div>
            </section>

            <!-- Animations Test -->
            <section class="test-section">
                <h2 class="neapolitan-title">Test Animazioni</h2>
                <div class="test-grid">
                    <div class="mystical-loader"></div>
                    <div class="golden-pulse" style="padding: var(--spacing-4); background: var(--cream); border-radius: var(--radius-lg);">
                        Pulsazione Dorata
                    </div>
                    <div class="mystical-pulse" style="padding: var(--spacing-4); background: var(--mystical-color); color: white; border-radius: var(--radius-lg);">
                        Pulsazione Mistica
                    </div>
                </div>
            </section>

            <!-- Background Patterns Test -->
            <section class="test-section parchment-texture">
                <h2 class="neapolitan-title">Test Texture Pergamena</h2>
                <p>Questa sezione mostra la texture di pergamena di sfondo.</p>
            </section>

            <section class="test-section traditional-pattern">
                <h2 class="neapolitan-title">Test Pattern Tradizionale</h2>
                <p>Questa sezione mostra il pattern tradizionale di sfondo.</p>
            </section>

            <!-- Stock Images Test -->
            <section class="test-section">
                <h2 class="neapolitan-title">Test Immagini Stock e Sfondi Tematici</h2>

                <div class="test-grid">
                    <div class="test-section mystical-stars-bg">
                        <h3>Stelle Mistiche</h3>
                        <p>Sfondo con stelle animate per atmosfera mistica</p>
                    </div>

                    <div class="test-section ancient-parchment-bg">
                        <h3>Pergamena Antica</h3>
                        <p>Texture di pergamena per documenti tradizionali</p>
                    </div>

                    <div class="test-section italian-ceramic-pattern">
                        <h3>Ceramica Italiana</h3>
                        <p>Pattern ispirato alla ceramica tradizionale</p>
                    </div>

                    <div class="test-section mediterranean-waves">
                        <h3>Onde Mediterranee</h3>
                        <p>Onde del mare del Golfo di Napoli</p>
                    </div>

                    <div class="test-section lucky-numbers-bg">
                        <h3>Numeri Fortunati</h3>
                        <p>Pattern con numeri della smorfia</p>
                    </div>

                    <div class="test-section dream-clouds-bg">
                        <h3>Nuvole dei Sogni</h3>
                        <p>Nuvole fluttuanti per l'interpretazione</p>
                    </div>
                </div>

                <div class="test-grid">
                    <div class="test-section fortune-wheel-bg">
                        <h3>Ruota della Fortuna</h3>
                        <p>Pattern rotante per i risultati</p>
                    </div>

                    <div class="test-section mystical-symbols-bg">
                        <h3>Simboli Mistici</h3>
                        <p>Simboli magici e onirici</p>
                    </div>

                    <div class="test-section vesuvius-silhouette">
                        <h3>Silhouette Vesuvio</h3>
                        <p>Profilo del vulcano napoletano</p>
                    </div>
                </div>
            </section>

        </div>
    </main>

    <script>
        // Test delle animazioni progressive
        document.addEventListener('DOMContentLoaded', () => {
            // Simula l'animazione di rivelazione dei numeri
            const numberCards = document.querySelectorAll('.demo-number-card');
            numberCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'rotateY(90deg) scale(0.8)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                    card.style.opacity = '1';
                    card.style.transform = 'rotateY(0deg) scale(1)';
                }, index * 200 + 500);
            });

            // Simula l'animazione dei simboli
            const symbols = document.querySelectorAll('.symbol-tag');
            symbols.forEach((symbol, index) => {
                symbol.style.opacity = '0';
                symbol.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    symbol.style.transition = 'all 0.3s ease-out';
                    symbol.style.opacity = '1';
                    symbol.style.transform = 'translateY(0)';
                }, index * 100 + 2000);
            });
        });
    </script>
</body>
</html>
