<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Endpoint - Smorfia Dreams</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .success { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #ffebee; padding: 15px; border-radius: 8px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 10px 5px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔍 Debug Endpoint - Smorfia Dreams</h1>
    
    <div class="info">
        <h3>📍 Informazioni Ambiente</h3>
        <p><strong>URL:</strong> <span id="currentUrl"></span></p>
        <p><strong>Hostname:</strong> <span id="hostname"></span></p>
        <p><strong>Protocol:</strong> <span id="protocol"></span></p>
        <p><strong>Endpoint rilevato:</strong> <span id="detectedEndpoint"></span></p>
    </div>

    <div>
        <button onclick="testEndpointDetection()">🔍 Test Rilevamento Endpoint</button>
        <button onclick="testNetlifyFunction()">🚀 Test Funzione Netlify</button>
        <button onclick="clearResults()">🧹 Pulisci</button>
    </div>

    <div id="results"></div>

    <script type="module">
        // Simula la logica di AIService
        function getApiEndpoint() {
            // Controlla se siamo su Netlify (più specifico)
            if (window.location.hostname.includes('netlify.app') || 
                window.location.hostname.includes('netlify.com')) {
                console.log('🌐 Ambiente rilevato: Netlify - usando funzione serverless');
                return '/.netlify/functions/interpret-dream';
            }
            
            // Controlla se siamo su HTTPS (probabilmente produzione)
            if (window.location.protocol === 'https:' && 
                !window.location.hostname.includes('localhost')) {
                console.log('🌐 Ambiente rilevato: Produzione HTTPS - usando funzione serverless');
                return '/.netlify/functions/interpret-dream';
            }
            
            // Sviluppo locale
            console.log('🌐 Ambiente rilevato: Sviluppo locale - usando proxy server');
            return 'http://localhost:3001/api/interpret-dream';
        }

        // Inizializzazione
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('hostname').textContent = window.location.hostname;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('detectedEndpoint').textContent = getApiEndpoint();

        // Test rilevamento endpoint
        window.testEndpointDetection = function() {
            const endpoint = getApiEndpoint();
            const isNetlify = window.location.hostname.includes('netlify.app') || 
                             window.location.hostname.includes('netlify.com');
            const isHTTPS = window.location.protocol === 'https:';
            const isLocalhost = window.location.hostname.includes('localhost');

            const result = {
                endpoint: endpoint,
                checks: {
                    isNetlify: isNetlify,
                    isHTTPS: isHTTPS,
                    isLocalhost: isLocalhost,
                    shouldUseNetlifyFunction: isNetlify || (isHTTPS && !isLocalhost)
                }
            };

            addResult('🔍 Test Rilevamento Endpoint', result, 'info');
        };

        // Test funzione Netlify
        window.testNetlifyFunction = async function() {
            const endpoint = '/.netlify/functions/interpret-dream';
            
            try {
                addResult('🚀 Testando funzione Netlify...', { endpoint }, 'info');
                
                const response = await fetch(endpoint, {
                    method: 'OPTIONS'
                });

                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    url: response.url
                };

                if (response.status === 200) {
                    addResult('✅ Funzione Netlify raggiungibile', result, 'success');
                } else {
                    addResult('❌ Funzione Netlify non raggiungibile', result, 'error');
                }

            } catch (error) {
                addResult('❌ Errore test funzione Netlify', { 
                    error: error.message,
                    stack: error.stack 
                }, 'error');
            }
        };

        // Aggiungi risultato
        function addResult(title, data, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = type;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
                <small>Timestamp: ${new Date().toLocaleTimeString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Pulisci risultati
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        // Auto-test
        setTimeout(() => {
            testEndpointDetection();
        }, 500);
    </script>
</body>
</html>
