/*
 * PROXY SERVER DEBUG per API Google Gemini
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Logging dettagliato
app.use((req, res, next) => {
  console.log(`🔍 ${req.method} ${req.path}`);
  console.log('📦 Body:', JSON.stringify(req.body, null, 2));
  console.log('🔗 Headers:', req.headers);
  next();
});

// Middleware di sicurezza
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:8000', 'http://127.0.0.1:8000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  message: { error: 'Troppe richieste, riprova più tardi' }
});
app.use('/api/', limiter);

// Middleware di validazione DEBUG
const validateRequest = (req, res, next) => {
  console.log('🔍 Validazione richiesta...');
  const { contents } = req.body;
  
  console.log('📋 Contents:', contents);
  console.log('📋 Type:', typeof contents);
  console.log('📋 Is Array:', Array.isArray(contents));
  
  if (!contents || !Array.isArray(contents)) {
    console.log('❌ Errore: contents mancante o non array');
    return res.status(400).json({ error: 'Formato richiesta non valido' });
  }
  
  console.log('📋 Contents[0]:', contents[0]);
  console.log('📋 Parts:', contents[0]?.parts);
  console.log('📋 Text:', contents[0]?.parts?.[0]?.text);
  
  if (!contents[0]?.parts?.[0]?.text) {
    console.log('❌ Errore: testo mancante');
    return res.status(400).json({ error: 'Testo mancante' });
  }
  
  const text = contents[0].parts[0].text;
  console.log('📏 Lunghezza testo:', text.length);
  
  if (text.length < 10 || text.length > 5000) {
    console.log('❌ Errore: lunghezza testo non valida');
    return res.status(400).json({ error: 'Lunghezza testo non valida' });
  }
  
  console.log('✅ Validazione OK');
  next();
};

// Endpoint per interpretazione sogni
app.post('/api/interpret-dream', validateRequest, async (req, res) => {
  console.log('🚀 Chiamata a Gemini API...');
  
  try {
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash-002:generateContent?key=${process.env.GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(req.body)
      }
    );

    console.log('📡 Risposta API status:', response.status);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Risposta API ricevuta');
    res.json(data);
    
  } catch (error) {
    console.error('❌ Errore API Gemini:', error);
    res.status(500).json({ 
      error: 'Servizio temporaneamente non disponibile' 
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`🚀 Proxy server DEBUG su http://localhost:${PORT}`);
});