/* 
 * LAYOUT.CSS - Layout principale e struttura app
 * Parte del progetto Smorfia Dreams PWA 
 */

/* === CONTAINER BASE === */
.container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* === HEADER === */
.app-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: var(--spacing-6) 0 var(--spacing-8);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.app-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
  animation: float 20s infinite linear;
  pointer-events: none;
}

@keyframes float {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  font-weight: 300;
}

/* === MAIN CONTENT === */
.main-content {
  flex: 1;
  padding: var(--spacing-6) 0 var(--spacing-20);
  min-height: calc(100vh - 200px);
}

/* === SCREEN MANAGEMENT === */
.screen {
  display: none;
  min-height: 400px;
}

.screen.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

/* === CARDS BASE === */
.welcome-card,
.input-card,
.loading-card,
.results-card,
.history-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-6);
}

/* === BOTTOM NAVIGATION === */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--spacing-2) 0;
  z-index: var(--z-dropdown);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 480px;
  margin: 0 auto;
}

.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2);
  background: none;
  border: none;
  color: var(--gray-500);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: color var(--transition-fast);
  border-radius: var(--radius-md);
}

.nav-btn:hover,
.nav-btn.active {
  color: var(--primary-color);
}

.nav-icon {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-1);
}

.nav-text {
  font-size: var(--font-size-xs);
  font-weight: 500;
}

/* === TOAST CONTAINER === */
.toast-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: var(--z-toast);
  pointer-events: none;
}

/* === RESPONSIVENESS === */
@media (min-width: 768px) {
  .container {
    max-width: 600px;
  }
  
  .main-content {
    padding: var(--spacing-8) 0 var(--spacing-20);
  }
  
  .welcome-card,
  .input-card,
  .loading-card,
  .results-card,
  .history-card {
    padding: var(--spacing-12);
  }
}

/* Body layout generale */
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* PWA safe areas per notch */
.app-header {
  padding-top: calc(var(--spacing-6) + env(safe-area-inset-top));
}

.bottom-nav {
  padding-bottom: calc(var(--spacing-2) + env(safe-area-inset-bottom));
}