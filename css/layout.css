/*
 * LAYOUT.CSS - Layout principale e struttura app
 * Parte del progetto Smorfia Dreams PWA
 */

/* === CONTAINER BASE === */
.container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* === HEADER === */
.app-header {
  background: var(--gradient-mystical);
  color: var(--white);
  padding: var(--spacing-6) 0 var(--spacing-8);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-mystical);
}

/* Pattern decorativo ispirato alla tradizione napoletana */
.app-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="rgba(255,255,255,0.08)"><circle cx="30" cy="30" r="3"/><path d="M30 15 L35 25 L45 25 L37 32 L40 42 L30 37 L20 42 L23 32 L15 25 L25 25 Z" fill="rgba(214,158,46,0.1)"/></g></svg>');
  animation: mysticalFloat 30s infinite linear;
  pointer-events: none;
}

/* Overlay con texture sottile */
.app-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 70%, rgba(214, 158, 46, 0.15) 0%, transparent 50%);
  pointer-events: none;
}

@keyframes mysticalFloat {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.app-title {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  position: relative;
  z-index: 1;
  letter-spacing: 0.5px;
}

.app-subtitle {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-lg);
  opacity: 0.95;
  font-weight: 400;
  font-style: italic;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* === MAIN CONTENT === */
.main-content {
  flex: 1;
  padding: var(--spacing-6) 0 var(--spacing-20);
  min-height: calc(100vh - 200px);
}

/* === SCREEN MANAGEMENT === */
.screen {
  display: none;
  min-height: 400px;
}

.screen.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

/* === CARDS BASE === */
.welcome-card,
.input-card,
.loading-card,
.results-card,
.history-card {
  background: var(--cream);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-6);
  position: relative;
  border: 1px solid var(--warm-200);
  transition: all var(--transition-normal);
}

/* Effetto hover per le card */
.welcome-card:hover,
.input-card:hover,
.results-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--accent-color);
}

/* Bordo decorativo sottile */
.welcome-card::before,
.input-card::before,
.loading-card::before,
.results-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-warm);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

/* === BOTTOM NAVIGATION === */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--spacing-2) 0;
  z-index: var(--z-dropdown);
}

.nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 480px;
  margin: 0 auto;
}

.nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2);
  background: none;
  border: none;
  color: var(--gray-500);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: color var(--transition-fast);
  border-radius: var(--radius-md);
}

.nav-btn:hover,
.nav-btn.active {
  color: var(--primary-color);
}

.nav-icon {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-1);
}

.nav-text {
  font-size: var(--font-size-xs);
  font-weight: 500;
}

/* === TOAST CONTAINER === */
.toast-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: var(--z-toast);
  pointer-events: none;
}

/* === RESPONSIVENESS === */
@media (min-width: 768px) {
  .container {
    max-width: 600px;
  }

  .main-content {
    padding: var(--spacing-8) 0 var(--spacing-20);
  }

  .welcome-card,
  .input-card,
  .loading-card,
  .results-card,
  .history-card {
    padding: var(--spacing-12);
  }
}

/* Body layout generale */
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom);
}

/* PWA safe areas per notch */
.app-header {
  padding-top: calc(var(--spacing-6) + env(safe-area-inset-top));
}

.bottom-nav {
  padding-bottom: calc(var(--spacing-2) + env(safe-area-inset-bottom));
}