/*
 * COMPONENTS.CSS - Stili per i componenti dell'app
 * Parte del progetto Smorfia Dreams PWA
 */

/* === BOTTONI === */
.primary-btn,
.secondary-btn,
.choice-btn,
.record-btn,
.back-btn {
  border: none;
  border-radius: var(--radius-xl);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-family: inherit;
}

.primary-btn {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-base);
  font-weight: 600;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-normal);
}

.primary-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.primary-btn:hover:not(:disabled)::before {
  left: 100%;
}

.primary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.secondary-btn {
  background: var(--white);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  margin: var(--spacing-2);
}

.secondary-btn:hover {
  background: var(--primary-color);
  color: var(--white);
}
.choice-btn {
  background: var(--cream);
  border: 2px solid var(--warm-200);
  padding: var(--spacing-6);
  margin: var(--spacing-3);
  width: 100%;
  font-size: var(--font-size-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.choice-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-warm);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.choice-btn:hover {
  border-color: var(--accent-color);
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  background: var(--white);
}

.choice-btn:hover::before {
  transform: scaleX(1);
}

.btn-icon {
  font-size: var(--font-size-4xl);
}

.back-btn {
  background: none;
  color: var(--gray-600);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
  align-self: flex-start;
}

.back-btn:hover {
  color: var(--primary-color);
  background: var(--gray-100);
}

/* === WELCOME SCREEN === */
.welcome-icon {
  font-size: 5rem;
  text-align: center;
  margin-bottom: var(--spacing-6);
  filter: drop-shadow(0 4px 8px rgba(214, 158, 46, 0.3));
  animation: floatUp 4s ease-in-out infinite;
}

.input-choice {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-top: var(--spacing-8);
}

/* === INPUT ELEMENTI === */
.text-input-container {
  position: relative;
  margin: var(--spacing-6) 0;
}

#dream-text {
  width: 100%;
  padding: var(--spacing-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: 1.6;
  resize: vertical;
  min-height: 120px;
}
#dream-text:focus {
  border-color: var(--primary-color);
  outline: none;
}

.char-count {
  position: absolute;
  bottom: var(--spacing-2);
  right: var(--spacing-3);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

/* === VOICE INPUT === */
.voice-input-container {
  text-align: center;
  padding: var(--spacing-8) 0;
}

.voice-visualizer {
  position: relative;
  width: 160px;
  height: 160px;
  margin: 0 auto var(--spacing-8);
  border-radius: 50%;
  background: var(--gradient-mystical);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-mystical);
  transition: all var(--transition-normal);
}

.pulse-circle {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  opacity: 0;
}

.voice-visualizer.recording .pulse-circle {
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.voice-icon {
  font-size: var(--font-size-4xl);
  color: var(--white);
}

.record-btn {
  background: var(--error-color);
  color: var(--white);
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
  margin: var(--spacing-4);
}
.record-btn.recording {
  background: var(--error-color);
  animation: pulse 1s infinite;
}

.recording-status {
  margin: var(--spacing-6) 0;
}

.recording-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  color: var(--error-color);
  font-weight: 600;
}

.recording-dot {
  width: 8px;
  height: 8px;
  background: var(--error-color);
  border-radius: 50%;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.transcription-area {
  margin-top: var(--spacing-6);
  text-align: left;
}

.transcription-text {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  margin: var(--spacing-4) 0;
  min-height: 100px;
  font-size: var(--font-size-base);
  line-height: 1.6;
}

/* === LOADING SCREEN === */
.loading-spinner {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-steps {
  display: flex;
  justify-content: center;
  gap: var(--spacing-6);
  margin-top: var(--spacing-8);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  opacity: 0.3;
  transition: opacity var(--transition-normal);
}

.step.active {
  opacity: 1;
}

.step-icon {
  font-size: var(--font-size-2xl);
}

.step-text {
  font-size: var(--font-size-xs);
  text-align: center;
}

/* === RESULTS SCREEN === */
.results-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.numbers-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.number-card {
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-6);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.number-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-warm);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.number-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.number-card:hover::before {
  opacity: 1;
}
.number-value {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
}

.number-meaning {
  font-size: var(--font-size-xs);
  opacity: 0.9;
  font-weight: 600;
}

.number-description {
  font-size: var(--font-size-xs);
  opacity: 0.8;
  margin-top: var(--spacing-1);
  font-style: italic;
  line-height: 1.3;
}

.interpretation-container,
.symbols-container {
  margin: var(--spacing-8) 0;
  padding: var(--spacing-6);
  background: var(--warm-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--warm-200);
  position: relative;
  box-shadow: var(--shadow-sm);
}

.interpretation-container::before,
.symbols-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-mystical);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.interpretation-text {
  line-height: 1.7;
  font-size: var(--font-size-base);
  color: var(--warm-700);
  font-family: var(--font-family-primary);
  font-style: italic;
}

.symbols-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
}

.symbol-tag {
  background: var(--gradient-warm);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.symbol-tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-color);
}

.actions-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  justify-content: center;
  margin-top: var(--spacing-8);
}

/* === HISTORY SCREEN === */
.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.history-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.history-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.history-date {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

.history-type {
  font-size: var(--font-size-lg);
}

.history-dream {
  color: var(--gray-700);
  line-height: 1.5;
  margin-bottom: var(--spacing-3);
}

.history-numbers {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--font-size-sm);
}

.history-empty {
  text-align: center;
  padding: var(--spacing-16);
  color: var(--gray-500);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-4);
}

/* === TOAST MESSAGES === */
.toast {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4) var(--spacing-6);
  margin-bottom: var(--spacing-2);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--primary-color);
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--transition-normal);
  max-width: 300px;
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

.toast-success {
  border-left-color: var(--success-color);
}

.toast-error {
  border-left-color: var(--error-color);
}

.toast-warning {
  border-left-color: var(--warning-color);
}
/* === SEZIONE SPIEGAZIONE NUMERI === */
.numbers-explanation-container {
  margin: var(--spacing-6) 0;
  padding: var(--spacing-6);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary-color);
}

.numbers-explanation-container h3 {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-4);
  font-weight: 600;
}

.numbers-explanation-text {
  line-height: 1.6;
  font-size: var(--font-size-base);
  color: var(--gray-700);
  font-style: italic;
}

/* === MIGLIORAMENTI INTERFACCIA RISULTATI === */
.interpretation-container h3,
.symbols-container h3 {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-4);
  font-weight: 600;
}

.number-card {
  position: relative;
  transition: all var(--transition-normal);
}

.number-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-color) 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.number-card:hover::after {
  opacity: 1;
}