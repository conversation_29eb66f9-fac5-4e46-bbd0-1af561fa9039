/*
 * ENHANCEMENTS.CSS - Miglioramenti visivi e tematici per Smorf-IA
 * Elementi decorativi ispirati alla tradizione napoletana e smorfia
 */

/* === ELEMENTI DECORATIVI === */

/* Icone tematiche personalizzate */
.mystical-icon {
  position: relative;
  display: inline-block;
}

.mystical-icon::after {
  content: '✨';
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.7em;
  opacity: 0.7;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Separatori decorativi */
.decorative-divider {
  width: 100%;
  height: 2px;
  background: var(--gradient-warm);
  margin: var(--spacing-6) 0;
  border-radius: var(--radius-full);
  position: relative;
}

.decorative-divider::before {
  content: '🌟';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--cream);
  padding: 0 var(--spacing-2);
  font-size: var(--font-size-sm);
}

/* === MIGLIORAMENTI TIPOGRAFICI === */

/* Titoli con stile napoletano */
.neapolitan-title {
  font-family: var(--font-family-primary);
  color: var(--primary-color);
  text-align: center;
  position: relative;
  margin-bottom: var(--spacing-6);
}

.neapolitan-title::before,
.neapolitan-title::after {
  content: '◆';
  color: var(--accent-color);
  margin: 0 var(--spacing-2);
  font-size: 0.8em;
}

/* Testo con enfasi mistica */
.mystical-text {
  font-family: var(--font-family-primary);
  font-style: italic;
  color: var(--mystical-color);
  text-shadow: 0 1px 2px rgba(85, 60, 154, 0.1);
}

/* === EFFETTI HOVER AVANZATI === */

/* Effetto shimmer per elementi interattivi */
.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.shimmer-effect:hover::before {
  left: 100%;
}

/* === PATTERN DI SFONDO === */

/* Pattern sottile per le card */
.traditional-pattern {
  background-image:
    radial-gradient(circle at 2px 2px, var(--warm-300) 1px, transparent 0);
  background-size: 20px 20px;
  opacity: 0.1;
}

/* Texture pergamena */
.parchment-texture {
  background: var(--cream);
  background-image:
    radial-gradient(circle at 25% 25%, rgba(214, 158, 46, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(197, 48, 48, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, transparent 40%, rgba(139, 115, 85, 0.02) 50%, transparent 60%);
}

/* === ANIMAZIONI SPECIALI === */

/* Animazione di rivelazione per i numeri */
@keyframes numberReveal {
  0% {
    opacity: 0;
    transform: rotateY(90deg) scale(0.8);
  }
  50% {
    opacity: 0.5;
    transform: rotateY(45deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: rotateY(0deg) scale(1);
  }
}

.number-reveal {
  animation: numberReveal 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Animazione di pulsazione mistica */
@keyframes mysticalPulse {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(85, 60, 154, 0.3),
      inset 0 0 20px rgba(85, 60, 154, 0.1);
  }
  50% {
    box-shadow:
      0 0 40px rgba(85, 60, 154, 0.6),
      inset 0 0 30px rgba(85, 60, 154, 0.2);
  }
}

.mystical-pulse {
  animation: mysticalPulse 3s ease-in-out infinite;
}

/* === ELEMENTI INTERATTIVI === */

/* Bottone con effetto magico */
.magic-button {
  position: relative;
  overflow: hidden;
  background: var(--gradient-mystical);
  border: none;
  color: var(--white);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-xl);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.magic-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.magic-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-mystical);
}

.magic-button:hover::before {
  left: 100%;
}

/* === INDICATORI DI STATO === */

/* Indicatore di caricamento mistico */
.mystical-loader {
  width: 40px;
  height: 40px;
  border: 3px solid var(--warm-200);
  border-top: 3px solid var(--mystical-color);
  border-radius: 50%;
  animation: mysticalSpin 1s linear infinite;
  margin: 0 auto;
}

@keyframes mysticalSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Indicatore di successo */
.success-indicator {
  color: var(--success-color);
  font-size: var(--font-size-2xl);
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* === IMMAGINI DI SFONDO TEMATICHE === */

/* Sfondo per la welcome card con pattern napoletano */
.welcome-card {
  background-image:
    radial-gradient(circle at 20% 80%, rgba(214, 158, 46, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(197, 48, 48, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, transparent 40%, rgba(85, 60, 154, 0.05) 50%, transparent 60%);
}

/* Sfondo mistico per le card dei risultati */
.results-card {
  background-image:
    radial-gradient(circle at 30% 70%, rgba(85, 60, 154, 0.08) 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, rgba(214, 158, 46, 0.03) 50%, transparent 70%);
}

/* Pattern decorativo per le card di input */
.input-card {
  background-image:
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 20px,
      rgba(214, 158, 46, 0.02) 20px,
      rgba(214, 158, 46, 0.02) 40px
    );
}

/* === ELEMENTI DECORATIVI AVANZATI === */

/* Cornice decorativa per i numeri */
.number-card {
  position: relative;
}

.number-card::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: conic-gradient(
    from 0deg,
    var(--accent-color),
    var(--secondary-color),
    var(--mystical-color),
    var(--accent-color)
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.number-card:hover::before {
  opacity: 0.3;
}

/* Effetto stella per elementi mistici */
.mystical-star {
  position: relative;
}

.mystical-star::before {
  content: '✦';
  position: absolute;
  top: -8px;
  right: -8px;
  color: var(--accent-color);
  font-size: 0.8em;
  animation: starTwinkle 2s ease-in-out infinite;
}

@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.6;
    transform: rotate(0deg) scale(1);
  }
  50% {
    opacity: 1;
    transform: rotate(180deg) scale(1.1);
  }
}

/* === MIGLIORAMENTI TIPOGRAFICI AVANZATI === */

/* Effetto ombra per testi importanti */
.shadow-text {
  text-shadow:
    0 1px 0 rgba(255, 255, 255, 0.5),
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Testo con gradiente */
.gradient-text {
  background: var(--gradient-warm);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* === ANIMAZIONI SPECIALI AGGIUNTIVE === */

/* Animazione di pulsazione dorata */
@keyframes goldenPulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(214, 158, 46, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(214, 158, 46, 0.6);
  }
}

.golden-pulse {
  animation: goldenPulse 2s ease-in-out infinite;
}

/* Animazione di rotazione mistica */
@keyframes mysticalRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.mystical-rotate {
  animation: mysticalRotate 20s linear infinite;
}

/* === RESPONSIVE ENHANCEMENTS === */

/* Miglioramenti per schermi piccoli */
@media (max-width: 480px) {
  .neapolitan-title {
    font-size: var(--font-size-xl);
  }

  .mystical-icon::after {
    font-size: 0.6em;
  }

  .decorative-divider::before {
    font-size: var(--font-size-xs);
  }

  .mystical-star::before {
    font-size: 0.6em;
    top: -6px;
    right: -6px;
  }
}

/* Miglioramenti per schermi grandi */
@media (min-width: 768px) {
  .traditional-pattern {
    background-size: 30px 30px;
  }

  .mystical-loader {
    width: 50px;
    height: 50px;
  }

  .welcome-card,
  .results-card,
  .input-card {
    background-attachment: fixed;
  }
}
