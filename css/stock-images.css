/*
 * STOCK-IMAGES.CSS - Stock images and visual backgrounds for Smorf-IA
 * Free/royalty-free visual elements for dream interpretation and Neapolitan smorfia theme
 */

/* === MYSTICAL DREAM BACKGROUNDS === */

/* Starry night pattern for mystical atmosphere */
.mystical-stars-bg {
  background-image:
    radial-gradient(2px 2px at 20px 30px, #ffd700, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #ffd700, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #ffd700, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: mysticalStars 20s linear infinite;
}

@keyframes mysticalStars {
  0% { background-position: 0 0, 0 0, 0 0, 0 0, 0 0; }
  100% { background-position: 200px 100px, -200px -100px, 100px -100px, -100px 100px, 200px 0; }
}

/* Ancient parchment texture */
.ancient-parchment-bg {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(139, 115, 85, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(214, 158, 46, 0.08) 0%, transparent 50%),
    linear-gradient(45deg, transparent 40%, rgba(197, 48, 48, 0.03) 50%, transparent 60%),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(139, 115, 85, 0.02) 2px,
      rgba(139, 115, 85, 0.02) 4px
    );
  background-size: 300px 300px, 200px 200px, 150px 150px, 20px 20px;
}

/* Crystal ball mystical aura */
.crystal-ball-aura {
  background:
    radial-gradient(circle at center,
      rgba(85, 60, 154, 0.3) 0%,
      rgba(85, 60, 154, 0.1) 30%,
      rgba(214, 158, 46, 0.1) 60%,
      transparent 100%
    );
  animation: crystalPulse 4s ease-in-out infinite;
}

@keyframes crystalPulse {
  0%, 100% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.1); opacity: 1; }
}

/* === NEAPOLITAN CULTURAL PATTERNS === */

/* Traditional Italian ceramic pattern */
.italian-ceramic-pattern {
  background-image:
    repeating-conic-gradient(
      from 0deg at 50% 50%,
      transparent 0deg,
      rgba(214, 158, 46, 0.1) 45deg,
      transparent 90deg,
      rgba(197, 48, 48, 0.1) 135deg,
      transparent 180deg
    ),
    radial-gradient(circle at 30% 70%, rgba(38, 161, 105, 0.05) 0%, transparent 50%);
  background-size: 60px 60px, 120px 120px;
}

/* Vesuvius silhouette pattern */
.vesuvius-silhouette {
  background-image:
    linear-gradient(to bottom, transparent 60%, rgba(26, 54, 93, 0.1) 60%, rgba(26, 54, 93, 0.2) 80%, rgba(26, 54, 93, 0.3) 100%),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 40px,
      rgba(214, 158, 46, 0.05) 40px,
      rgba(214, 158, 46, 0.05) 80px
    );
}

/* Mediterranean sea waves */
.mediterranean-waves {
  background-image:
    repeating-linear-gradient(
      0deg,
      rgba(44, 82, 130, 0.1) 0px,
      rgba(44, 82, 130, 0.2) 10px,
      rgba(44, 82, 130, 0.1) 20px,
      transparent 30px,
      transparent 40px
    );
  animation: waveMotion 8s ease-in-out infinite;
}

@keyframes waveMotion {
  0%, 100% { background-position: 0 0; }
  50% { background-position: 0 20px; }
}

/* === LOTTERY AND FORTUNE SYMBOLS === */

/* Lucky numbers pattern */
.lucky-numbers-bg {
  background-image:
    url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23d69e2e' fill-opacity='0.1'%3E%3Ctext x='20' y='25' text-anchor='middle' font-size='16' font-weight='bold'%3E7%3C/text%3E%3C/g%3E%3C/svg%3E"),
    url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23c53030' fill-opacity='0.08'%3E%3Ctext x='20' y='25' text-anchor='middle' font-size='14' font-weight='bold'%3E13%3C/text%3E%3C/g%3E%3C/svg%3E"),
    url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23553c9a' fill-opacity='0.06'%3E%3Ctext x='20' y='25' text-anchor='middle' font-size='12' font-weight='bold'%3E42%3C/text%3E%3C/g%3E%3C/svg%3E");
  background-size: 80px 80px, 120px 120px, 100px 100px;
  background-position: 0 0, 40px 40px, 20px 60px;
}

/* Fortune wheel pattern */
.fortune-wheel-bg {
  background-image:
    conic-gradient(
      from 0deg,
      rgba(214, 158, 46, 0.1) 0deg,
      rgba(197, 48, 48, 0.1) 60deg,
      rgba(85, 60, 154, 0.1) 120deg,
      rgba(38, 161, 105, 0.1) 180deg,
      rgba(214, 158, 46, 0.1) 240deg,
      rgba(197, 48, 48, 0.1) 300deg,
      rgba(214, 158, 46, 0.1) 360deg
    );
  background-size: 200px 200px;
  background-position: center;
  /* Animation removed for better text readability */
}

/* === DREAM INTERPRETATION SYMBOLS === */

/* Dream clouds pattern */
.dream-clouds-bg {
  background-image:
    radial-gradient(ellipse 80px 40px at 40px 40px, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
    radial-gradient(ellipse 60px 30px at 120px 80px, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse 100px 50px at 200px 30px, rgba(255, 255, 255, 0.25) 0%, transparent 50%);
  background-size: 240px 120px;
  animation: cloudDrift 25s ease-in-out infinite;
}

@keyframes cloudDrift {
  0%, 100% { background-position: 0 0; }
  50% { background-position: 50px 10px; }
}

/* Mystical symbols pattern */
.mystical-symbols-bg {
  background-image:
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23553c9a' fill-opacity='0.08'%3E%3Ctext x='30' y='35' text-anchor='middle' font-size='20'%3E✦%3C/text%3E%3C/g%3E%3C/svg%3E"),
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23d69e2e' fill-opacity='0.06'%3E%3Ctext x='30' y='35' text-anchor='middle' font-size='18'%3E🌙%3C/text%3E%3C/g%3E%3C/svg%3E"),
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23c53030' fill-opacity='0.05'%3E%3Ctext x='30' y='35' text-anchor='middle' font-size='16'%3E✨%3C/text%3E%3C/g%3E%3C/svg%3E");
  background-size: 120px 120px, 80px 80px, 100px 100px;
  background-position: 0 0, 60px 60px, 30px 90px;
}

/* === CARD-SPECIFIC BACKGROUNDS === */

/* Welcome card with mystical atmosphere */
.welcome-card-bg {
  background-image:
    var(--gradient-background),
    radial-gradient(circle at 20% 80%, rgba(214, 158, 46, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(85, 60, 154, 0.1) 0%, transparent 50%);
  background-blend-mode: normal, overlay, multiply;
}

/* Results card with celebration theme */
.results-card-bg {
  background-image:
    var(--gradient-background),
    radial-gradient(circle at 50% 50%, rgba(214, 158, 46, 0.2) 0%, transparent 70%),
    repeating-conic-gradient(
      from 0deg at 50% 50%,
      transparent 0deg,
      rgba(197, 48, 48, 0.05) 30deg,
      transparent 60deg
    );
  background-size: 100% 100%, 300px 300px, 150px 150px;
}

/* Loading card with mystical energy */
.loading-card-bg {
  background-image:
    var(--gradient-background),
    radial-gradient(circle at center, rgba(85, 60, 154, 0.15) 0%, transparent 60%),
    conic-gradient(
      from 0deg,
      rgba(214, 158, 46, 0.1) 0deg,
      rgba(85, 60, 154, 0.1) 120deg,
      rgba(197, 48, 48, 0.1) 240deg,
      rgba(214, 158, 46, 0.1) 360deg
    );
  background-size: 100% 100%, 200px 200px, 400px 400px;
  animation: mysticalEnergy 15s linear infinite;
}

@keyframes mysticalEnergy {
  0% { background-position: center, center, 0 0; }
  100% { background-position: center, center, 400px 400px; }
}

/* === RESPONSIVE ADJUSTMENTS === */

@media (max-width: 768px) {
  .mystical-stars-bg { background-size: 150px 75px; }
  .italian-ceramic-pattern { background-size: 40px 40px, 80px 80px; }
  .lucky-numbers-bg { background-size: 60px 60px, 90px 90px, 75px 75px; }
  .fortune-wheel-bg { background-size: 150px 150px; }
  .dream-clouds-bg { background-size: 180px 90px; }
  .mystical-symbols-bg { background-size: 90px 90px, 60px 60px, 75px 75px; }
}

@media (prefers-reduced-motion: reduce) {
  .mystical-stars-bg,
  .dream-clouds-bg,
  .loading-card-bg,
  .crystal-ball-aura,
  .mediterranean-waves {
    animation: none;
  }
}
