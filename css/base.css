/*
 * BASE.CSS - Stili di base e variabili CSS
 * Parte del progetto Smorfia Dreams PWA
 */

/* === CSS CUSTOM PROPERTIES (Variabili) === */
:root {
  /* Colori principali - Tema Smorfia Napoletana */
  --primary-color: #1a365d;        /* Blu profondo mediterraneo */
  --primary-dark: #0f2a44;         /* Blu notte napoletana */
  --primary-light: #2c5282;        /* Blu mare del golfo */

  /* Colori secondari - Tradizione italiana */
  --secondary-color: #c53030;      /* Rosso tradizionale */
  --accent-color: #d69e2e;         /* Oro antico */
  --tertiary-color: #38a169;       /* Verde salvia */
  --mystical-color: #553c9a;       /* Viola mistico */

  /* Colori funzionali */
  --success-color: #38a169;        /* Verde successo */
  --warning-color: #d69e2e;        /* Ambra avvertimento */
  --error-color: #e53e3e;          /* <PERSON><PERSON> errore */

  /* Colori neutri - Palette calda */
  --white: #ffffff;
  --cream: #fefcf3;                /* Crema pergamena */
  --warm-50: #faf8f3;              /* Bianco caldo */
  --warm-100: #f4f1e8;             /* Grigio caldissimo */
  --warm-200: #e8e2d4;             /* Beige chiaro */
  --warm-300: #d4c5a9;             /* Sabbia */
  --warm-400: #a8956b;             /* Bronzo chiaro */
  --warm-500: #8b7355;             /* Bronzo */
  --warm-600: #6b5b47;             /* Bronzo scuro */
  --warm-700: #4a3f35;             /* Marrone caldo */
  --warm-800: #2d251e;             /* Marrone profondo */
  --warm-900: #1a1611;             /* Quasi nero caldo */

  /* Tipografia - Ispirata alla tradizione italiana */
  --font-family-primary: 'Georgia', 'Times New Roman', serif;
  --font-family-secondary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-accent: 'Brush Script MT', cursive;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  /* Spaziature */
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */

  /* Border radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;  /* Circular */

  /* Shadows - Più calde e profonde */
  --shadow-sm: 0 1px 2px 0 rgba(26, 22, 17, 0.08);
  --shadow-md: 0 4px 6px -1px rgba(26, 22, 17, 0.12), 0 2px 4px -1px rgba(26, 22, 17, 0.08);
  --shadow-lg: 0 10px 15px -3px rgba(26, 22, 17, 0.15), 0 4px 6px -2px rgba(26, 22, 17, 0.08);
  --shadow-xl: 0 20px 25px -5px rgba(26, 22, 17, 0.15), 0 10px 10px -5px rgba(26, 22, 17, 0.06);
  --shadow-mystical: 0 8px 32px rgba(85, 60, 154, 0.15), 0 4px 16px rgba(85, 60, 154, 0.1);

  /* Transizioni - Più fluide e naturali */
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Gradienti tematici */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-mystical: linear-gradient(135deg, var(--mystical-color) 0%, var(--primary-color) 100%);
  --gradient-warm: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
  --gradient-background: linear-gradient(135deg, var(--warm-50) 0%, var(--cream) 100%);

  /* Z-index layers */
  --z-dropdown: 1000;
  --z-modal: 1100;
  --z-toast: 1200;
  --z-tooltip: 1300;
}

/* === RESET CSS === */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--warm-800);
  background: var(--gradient-background);
  background-attachment: fixed;
  overflow-x: hidden;
  position: relative;
}

/* Texture di sfondo sottile */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(214, 158, 46, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(197, 48, 48, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* === UTILITY CLASSES === */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-accent { color: var(--accent-color); }
.text-mystical { color: var(--mystical-color); }
.text-warm-500 { color: var(--warm-500); }
.text-warm-600 { color: var(--warm-600); }
.text-warm-700 { color: var(--warm-700); }

.bg-white { background-color: var(--white); }
.bg-cream { background-color: var(--cream); }
.bg-primary { background-color: var(--primary-color); }
.bg-warm-50 { background-color: var(--warm-50); }

/* Classi per font families */
.font-primary { font-family: var(--font-family-primary); }
.font-secondary { font-family: var(--font-family-secondary); }
.font-accent { font-family: var(--font-family-accent); }

/* Focus states per accessibilità */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Animazioni base potenziate */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes mysticalGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(85, 60, 154, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(85, 60, 154, 0.6);
  }
}

@keyframes floatUp {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal);
}

.animate-slide-up {
  animation: slideInUp var(--transition-normal);
}

.animate-slide-down {
  animation: slideInDown var(--transition-normal);
}

.animate-mystical-glow {
  animation: mysticalGlow 3s ease-in-out infinite;
}

.animate-float {
  animation: floatUp 3s ease-in-out infinite;
}