/* 
 * RESPONSIVE.CSS - Media queries e adattamenti per diversi schermi
 * Parte del progetto Smorfia Dreams PWA 
 */

/* === MOBILE FIRST === */
/* Gli stili base sono già otti<PERSON>zzati per mobile */

/* === TABLET (768px+) === */
@media (min-width: 768px) {
  .container {
    max-width: 600px;
    padding: 0 var(--spacing-6);
  }
  
  .app-title {
    font-size: var(--font-size-4xl);
  }
  
  .choice-btn {
    flex-direction: row;
    text-align: left;
    padding: var(--spacing-8);
  }
  
  .btn-icon {
    margin-right: var(--spacing-4);
    font-size: var(--font-size-3xl);
  }
  
  .input-choice {
    flex-direction: row;
    gap: var(--spacing-6);
  }
  
  .choice-btn {
    flex: 1;
  }
  
  #dream-text {
    min-height: 150px;
    font-size: var(--font-size-lg);
  }
}

/* === DESKTOP (1024px+) === */
@media (min-width: 1024px) {
  .container {
    max-width: 800px;
  }
  
  .main-content {
    padding: var(--spacing-12) 0 var(--spacing-20);
  }
  
  .welcome-card,
  .input-card,
  .loading-card,
  .results-card,
  .history-card {
    padding: var(--spacing-16);
  }
  
  .input-choice {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .bottom-nav {
    display: none; /* Su desktop si può nascondere la nav bottom */
  }
  
  .main-content {
    padding-bottom: var(--spacing-8); /* Rimuove padding per nav bottom */
  }
}

/* === LANDSCAPE MOBILE === */
@media (max-height: 500px) and (orientation: landscape) {
  .app-header {
    padding: var(--spacing-3) 0 var(--spacing-4);
  }
  
  .app-title {
    font-size: var(--font-size-2xl);
  }
  
  .app-subtitle {
    font-size: var(--font-size-base);
  }
  
  .main-content {
    padding: var(--spacing-4) 0 var(--spacing-16);
  }
  
  .welcome-card,
  .input-card,
  .loading-card,
  .results-card,
  .history-card {
    padding: var(--spacing-6);
  }
}