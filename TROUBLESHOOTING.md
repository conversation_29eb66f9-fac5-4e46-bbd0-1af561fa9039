# 🔧 Troubleshooting - Smorf-IA su Netlify

## ❌ **Errore 500 - Funzione Netlify**

### Problema
```
POST https://smorf-ia.netlify.app/.netlify/functions/interpret-dream 500 (Internal Server Error)
```

### Soluzioni

#### 1. **Configura la variabile GEMINI_API_KEY**
1. Vai su [Netlify Dashboard](https://app.netlify.com)
2. Seleziona il tuo sito "smorf-ia"
3. Vai su **Site settings** > **Environment variables**
4. Clicca **Add variable**
5. Aggiungi:
   - **Key:** `GEMINI_API_KEY`
   - **Value:** La tua API key di Google Gemini

#### 2. **Ottieni una API Key di Google Gemini**
1. Vai su [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Accedi con il tuo account Google
3. Clicca "Create API Key"
4. Copia la chiave generata
5. Incollala nelle variabili d'ambiente di Netlify

#### 3. **Verifica i Log**
1. Su Netlify Dashboard > Functions
2. Clicca su "interpret-dream"
3. Controlla i log per errori specifici

---

## ⚠️ **Errore Icona PWA**

### Problema
```
Error while trying to use the following icon from the Manifest:
https://smorf-ia.netlify.app/assets/icon-144.png
(Resource size is not correct - typo in the Manifest?)
```

### Soluzione
✅ **Già risolto** - Il manifest.json è stato aggiornato con le dimensioni corrette.

---

## 🎤 **Errore Permessi Microfono**

### Problema
```
Permissions policy violation: microphone is not allowed in this document.
```

### Soluzione
✅ **Già risolto** - Il file netlify.toml è stato aggiornato per permettere l'uso del microfono.

---

## 🔍 **Come Verificare che Tutto Funzioni**

### 1. **Test API Gemini**
Apri la console del browser e testa direttamente:

```javascript
fetch('/.netlify/functions/interpret-dream', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    contents: [{
      parts: [{ text: 'Test sogno: ho sognato di volare' }]
    }]
  })
})
.then(r => r.json())
.then(console.log)
.catch(console.error);
```

### 2. **Test PWA**
1. Apri l'app su mobile
2. Dovrebbe apparire il banner "Installa app"
3. L'icona dovrebbe essere visibile correttamente

### 3. **Test Microfono**
1. Clicca su "Racconta a voce"
2. Dovrebbe chiedere i permessi microfono
3. Non dovrebbero apparire errori nella console

---

## 📱 **Modalità Fallback**

### Come Funziona
Se l'API Gemini non è disponibile, l'app usa automaticamente la **modalità fallback**:
- ✅ Genera numeri basati su parole chiave
- ✅ Crea interpretazioni plausibili
- ✅ L'utente può comunque usare l'app

### Verifica Fallback
Guarda nella console per:
```
⚠️ Errore connessione proxy, modalità fallback attiva
```

---

## 🚀 **Prossimi Passi**

### Dopo aver risolto l'errore 500:
1. **Redeploy** - Carica nuovamente i file aggiornati
2. **Test completo** - Prova tutte le funzionalità
3. **Condividi** - L'app è pronta per gli utenti!

### Per aggiornamenti futuri:
1. Modifica i file localmente
2. Carica su Netlify (drag & drop o Git)
3. Le modifiche sono live immediatamente

---

## 📞 **Supporto**

Se i problemi persistono:
1. Controlla i **Function logs** su Netlify
2. Verifica la **console del browser**
3. Assicurati che la **API key sia valida**

L'app è progettata per funzionare anche senza API, quindi gli utenti possono sempre usarla!
