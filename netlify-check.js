#!/usr/bin/env node

/**
 * NETLIFY DEPLOYMENT CHECKER
 * Script per verificare che tutti i file necessari siano presenti
 * prima del deploy su Netlify
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifica preparazione deploy Netlify...\n');

const checks = [
  {
    name: 'Funzione Netlify',
    path: 'netlify/functions/interpret-dream.js',
    required: true
  },
  {
    name: 'Configurazione Netlify',
    path: 'netlify.toml',
    required: true
  },
  {
    name: 'Redirects SPA',
    path: '_redirects',
    required: true
  },
  {
    name: 'Manifest PWA',
    path: 'manifest.json',
    required: true
  },
  {
    name: 'Service Worker',
    path: 'sw.js',
    required: true
  },
  {
    name: 'Index HTML',
    path: 'index.html',
    required: true
  },
  {
    name: 'Database Smorfia',
    path: 'data/smorfia.json',
    required: true
  },
  {
    name: 'Icona 144px PNG',
    path: 'assets/icon-144.png',
    required: false
  },
  {
    name: 'Icona 192px PNG',
    path: 'assets/icon-192.png',
    required: false
  },
  {
    name: 'Favicon',
    path: 'assets/favicon.ico',
    required: false
  }
];

let allGood = true;
let warnings = 0;

checks.forEach(check => {
  const exists = fs.existsSync(check.path);
  const status = exists ? '✅' : (check.required ? '❌' : '⚠️');
  
  console.log(`${status} ${check.name}: ${check.path}`);
  
  if (!exists && check.required) {
    allGood = false;
  } else if (!exists && !check.required) {
    warnings++;
  }
});

console.log('\n📋 Verifica configurazione...');

// Verifica netlify.toml
if (fs.existsSync('netlify.toml')) {
  const netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
  if (netlifyConfig.includes('directory = "netlify/functions"')) {
    console.log('✅ Directory funzioni configurata');
  } else {
    console.log('❌ Directory funzioni non configurata');
    allGood = false;
  }
}

// Verifica funzione Netlify
if (fs.existsSync('netlify/functions/interpret-dream.js')) {
  const functionCode = fs.readFileSync('netlify/functions/interpret-dream.js', 'utf8');
  if (functionCode.includes('process.env.GEMINI_API_KEY')) {
    console.log('✅ Variabile ambiente GEMINI_API_KEY configurata');
  } else {
    console.log('❌ Variabile ambiente GEMINI_API_KEY mancante');
    allGood = false;
  }
  
  if (functionCode.includes('Access-Control-Allow-Origin')) {
    console.log('✅ Headers CORS configurati');
  } else {
    console.log('❌ Headers CORS mancanti');
    allGood = false;
  }
}

// Verifica AI service
if (fs.existsSync('js/ai.js')) {
  const aiCode = fs.readFileSync('js/ai.js', 'utf8');
  if (aiCode.includes('/.netlify/functions/interpret-dream')) {
    console.log('✅ Endpoint Netlify configurato in AI service');
  } else {
    console.log('❌ Endpoint Netlify non configurato in AI service');
    allGood = false;
  }
}

console.log('\n📊 Risultato verifica:');
console.log(`✅ Controlli superati: ${checks.filter(c => fs.existsSync(c.path)).length}/${checks.length}`);
console.log(`⚠️  Warning: ${warnings}`);

if (allGood) {
  console.log('\n🎉 TUTTO PRONTO PER IL DEPLOY SU NETLIFY!');
  console.log('\n📝 Prossimi passi:');
  console.log('1. Crea un account su netlify.com');
  console.log('2. Collega il repository Git o carica i file');
  console.log('3. Configura la variabile GEMINI_API_KEY');
  console.log('4. Deploy automatico!');
  console.log('\n📖 Leggi NETLIFY-DEPLOY.md per istruzioni dettagliate');
} else {
  console.log('\n❌ PROBLEMI RILEVATI - Risolvi gli errori prima del deploy');
  process.exit(1);
}

if (warnings > 0) {
  console.log('\n⚠️  Alcuni file opzionali mancano ma il deploy dovrebbe funzionare');
}
