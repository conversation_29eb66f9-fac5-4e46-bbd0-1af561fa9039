# 🌙 Smorfia Dreams - Interprete AI dei Sogni

Una Progressive Web App (PWA) che interpreta i sogni utilizzando l'intelligenza artificiale e genera numeri per il lotto basati sulla tradizione della smorfia napoletana.

## 🎯 Caratteristiche

- **Input multiplo**: Racconta il sogno scrivendo o a voce
- **Interpretazione AI**: Utilizza Google Gemini 2.5 per l'analisi
- **Database Smorfia**: Numeri tradizionali della smorfia napoletana
- **PWA**: Installabile su mobile e desktop
- **Offline**: Funzionalità base disponibili offline
- **Storico**: Salvataggio locale dei sogni interpretati

## 🚀 Installazione

### Prerequisiti
- Browser moderno (Chrome, Safari, Firefox)
- Server web locale (per sviluppo)

### Setup locale

1. **Clona o scarica il progetto**
   ```bash
   git clone [url-progetto]
   cd SMORFIA
   ```

2. **Avvia un server locale**
   
   **Con Python:**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```
   
   **Con Node.js:**
   ```bash
   npx serve .
   ```
   
   **Con PHP:**
   ```bash
   php -S localhost:8000
   ```

3. **Apri nel browser**
   ```
   http://localhost:8000
   ```

## ⚙️ Configurazione API

**IMPORTANTE**: Per il funzionamento completo, è necessario configurare l'API di Google Gemini.

### Opzione 1: Proxy Server (Raccomandato)
Crea un proxy server che nasconda la tua API key:

```javascript
// Nel file js/ai.js, modifica l'endpoint
this.apiEndpoint = 'https://tuo-proxy.com/api/gemini';
```

### Opzione 2: Variabile d'ambiente (Solo per sviluppo)
```javascript
// js/ai.js
this.apiKey = process.env.GEMINI_API_KEY; // Non sicuro per produzione!
```

### Ottenere la API Key
1. Vai su [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Crea una nuova API key
3. Configura il proxy server con la tua chiave

## 📱 Utilizzo

### Come Interprete i Sogni

1. **Scegli il metodo di input**
   - 📝 **Testo**: Scrivi il sogno nel campo di testo
   - 🎤 **Voce**: Registra il racconto del sogno

2. **Racconta il sogno**
   - Descrivi più dettagli possibili
   - Include colori, persone, luoghi, emozioni
   - Minimo 10 caratteri

3. **Ottieni i risultati**
   - 5 numeri da giocare al lotto
   - Interpretazione dettagliata
   - Simboli identificati nel sogno

4. **Salva o condividi**
   - Salva nel tuo storico personale
   - Condividi con amici

### Funzionalità Offline

Anche senza connessione puoi:
- Consultare lo storico dei sogni
- Navigare nell'app
- Vedere i numeri salvati
## 🏗️ Struttura del Progetto

```
SMORFIA/
├── index.html              # Pagina principale
├── manifest.json           # Configurazione PWA
├── sw.js                   # Service Worker
├── README.md              # Documentazione
├── css/
│   ├── base.css           # Variabili e reset
│   ├── layout.css         # Layout principale
│   ├── components.css     # Componenti UI
│   └── responsive.css     # Media queries
├── js/
│   ├── app.js            # App principale
│   ├── audio.js          # Gestione audio/voce
│   ├── ai.js             # Servizio AI
│   ├── smorfia.js        # Database smorfia
│   └── storage.js        # Storage locale
├── data/
│   └── smorfia.json      # Numeri smorfia
└── assets/
    └── (icone PWA)
```

## 🔧 Tecnologie Utilizzate

- **Frontend**: HTML5, CSS3, Vanilla JavaScript ES6+
- **PWA**: Service Worker, Web App Manifest
- **Storage**: IndexedDB per dati locali
- **Audio**: Web Speech API per speech-to-text
- **AI**: Google Gemini 2.5 API
- **Styling**: CSS Custom Properties, Flexbox, Grid

## 🎨 Personalizzazione

### Modificare i Colori
Edita le variabili in `css/base.css`:

```css
:root {
  --primary-color: #tu-colore;
  --secondary-color: #altro-colore;
}
```

### Aggiungere Numeri Smorfia
Modifica `data/smorfia.json`:

```json
{
  "numero": "51",
  "significato": "Il tuo significato",
  "simboli": ["simbolo1", "simbolo2"],
  "descrizione": "Descrizione dettagliata"
}
```

### Customizzare l'AI
Modifica il prompt in `js/ai.js` nella funzione `createSmorfiaPrompt()`.

## 🚀 Deploy in Produzione

### Netlify (Raccomandato)
1. Fai il push del codice su GitHub
2. Connetti il repository a Netlify
3. Deploy automatico ad ogni commit

### Vercel
```bash
npm i -g vercel
vercel --prod
```

### GitHub Pages
1. Abilita GitHub Pages nel repository
2. Seleziona la branch main
3. L'app sarà disponibile su `username.github.io/repo-name`

## 🔒 Sicurezza

### Checklist Implementata

✅ **API Key Protection**: Non esposta nel frontend  
✅ **Input Sanitization**: Validazione testo utente  
✅ **HTTPS**: Obbligatorio per PWA  
✅ **CSP Headers**: Content Security Policy  
✅ **Rate Limiting**: Protezione da abusi API  
✅ **Local Storage**: Dati sensibili solo in locale  
✅ **Error Handling**: Messaggi sicuri per l'utente  

## 🐛 Troubleshooting

### L'app non si carica
- Verifica che il server locale sia attivo
- Controlla la console del browser per errori
- Assicurati che tutti i file siano presenti

### Speech Recognition non funziona
- Verifica i permissions del microfono
- Usa HTTPS (richiesto per API vocali)
- Controlla il supporto browser

### API AI non risponde
- Controlla la configurazione della API key
- Verifica la connessione internet
- Controlla i limiti di quota Google AI

### PWA non si installa
- Verifica che il manifest.json sia valido
- Usa HTTPS
- Controlla che il Service Worker sia registrato

## 📊 Performance

- **First Load**: < 3 secondi
- **Successive loads**: Istantaneo (cache)
- **PWA Size**: < 5MB
- **Offline**: Storico sempre disponibile

## 🤝 Contribuire

1. Fork del progetto
2. Crea un branch per la feature: `git checkout -b feature/nuova-feature`
3. Commit delle modifiche: `git commit -m 'Aggiunge nuova feature'`
4. Push del branch: `git push origin feature/nuova-feature`
5. Apri una Pull Request

## 📄 Licenza

Progetto sviluppato per scopi educativi e di intrattenimento.

## 🙏 Ringraziamenti

- Tradizione della Smorfia Napoletana
- Google Gemini AI
- Community degli sviluppatori PWA

---

**Sviluppato con ❤️ per gli appassionati della smorfia**

> ⚠️ **Nota**: Questo progetto è solo per intrattenimento. I numeri generati non garantiscono vincite al lotto!
