<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with Neapolitan theme gradient -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1a365d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f2a44;stop-opacity:1" />
    </radialGradient>
    <radialGradient id="moonGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d69e2e;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="96" cy="96" r="96" fill="url(#bgGradient)"/>
  
  <!-- Decorative border -->
  <circle cx="96" cy="96" r="88" fill="none" stroke="#d69e2e" stroke-width="2" opacity="0.3"/>
  
  <!-- Main moon symbol -->
  <text x="96" y="120" font-family="Arial, sans-serif" font-size="64" fill="url(#moonGradient)" text-anchor="middle">🌙</text>
  
  <!-- Mystical stars -->
  <text x="140" y="60" font-family="Arial, sans-serif" font-size="16" fill="#ffd700" text-anchor="middle" opacity="0.8">✨</text>
  <text x="52" y="50" font-family="Arial, sans-serif" font-size="12" fill="#ffd700" text-anchor="middle" opacity="0.6">⭐</text>
  <text x="150" y="140" font-family="Arial, sans-serif" font-size="14" fill="#ffd700" text-anchor="middle" opacity="0.7">✦</text>
  
  <!-- App name text -->
  <text x="96" y="170" font-family="Arial, sans-serif" font-size="14" fill="#ffd700" text-anchor="middle" font-weight="bold" opacity="0.9">Smorf-IA</text>
</svg>
